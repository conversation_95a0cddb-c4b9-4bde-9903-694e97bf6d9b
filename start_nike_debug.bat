@echo off
chcp 65001 >nul
title Nike小程序Frida调试

echo.
echo 🚀 Nike小程序Frida调试启动器
echo =====================================
echo.

REM 检查Frida是否安装
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Frida未安装！
    echo.
    echo 请先安装Frida:
    echo   pip install frida-tools
    echo.
    pause
    exit /b 1
)

echo ✅ Frida已安装
echo.

REM 检查设备连接
echo 📱 检查USB设备连接...
frida-ls-devices
echo.

REM 检查微信是否运行
echo 🔍 检查微信进程...
frida-ps -U | findstr /i wechat >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到微信进程！
    echo.
    echo 请确保:
    echo   1. 手机已连接并开启USB调试
    echo   2. 微信应用正在运行
    echo   3. frida-server已在手机上运行
    echo.
    echo 启动frida-server命令:
    echo   adb shell "su -c '/data/local/tmp/frida-server &'"
    echo.
    pause
    exit /b 1
)

echo ✅ 微信进程已找到
echo.

echo 🎯 启动Nike小程序Hook调试...
echo.
echo 📋 使用说明:
echo   1. 在手机上打开Nike小程序
echo   2. 执行登录和签到操作
echo   3. 在终端中输入以下命令查看数据:
echo      - summary()     : 查看捕获数据摘要
echo      - exportData()  : 导出所有数据
echo      - getLatestWxCode() : 获取最新微信Code
echo      - getLatestNikeToken() : 获取最新Nike Token
echo.
echo 🔄 正在启动Hook...
echo =====================================
echo.

REM 启动Frida Hook
frida -U com.tencent.mm -l nike_frida_debug.js

echo.
echo 🔚 调试会话已结束
pause
