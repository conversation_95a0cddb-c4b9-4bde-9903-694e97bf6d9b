/**
 * Nike小程序Frida调试脚本 - 集成版
 * 适用于电脑终端调试
 * 使用方法: frida -U -f com.tencent.mm -l nike_frida_debug.js --no-pause
 * 或者: frida -U com.tencent.mm -l nike_frida_debug.js
 */

console.log("\n🚀 Nike小程序Frida调试脚本已启动");
console.log("=====================================");
console.log("📱 目标应用: 微信 (com.tencent.mm)");
console.log("🎯 目标小程序: Nike小程序 (wx096c43d1829a7788)");
console.log("=====================================\n");

// 全局数据存储
var hookData = {
    wxCodes: [],
    nikeTokens: [],
    signInRequests: [],
    cloudCalls: [],
    httpRequests: []
};

// 颜色输出函数
function colorLog(message, color = 'white') {
    const colors = {
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m',
        white: '\x1b[37m',
        reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

Java.perform(function() {
    colorLog("[+] 开始初始化Hook...", 'green');
    
    // ==================== 1. Hook微信登录获取Code ====================
    try {
        var WXLogin = Java.use("com.tencent.mm.plugin.appbrand.jsapi.auth.JsApiLogin");
        colorLog("[✓] 找到微信登录类", 'green');
        
        WXLogin.login.implementation = function(params, callback) {
            colorLog("\n[🔑] 检测到微信登录调用", 'cyan');
            
            var self = this;
            var originalCallback = callback;
            
            // 创建Hook回调
            var hookCallback = Java.registerClass({
                name: "com.nike.debug.LoginCallback" + Date.now(),
                implements: [Java.use("com.tencent.mm.plugin.appbrand.jsapi.auth.LoginCallback")],
                methods: {
                    onSuccess: function(result) {
                        try {
                            var code = result.get("code");
                            if (code) {
                                hookData.wxCodes.push({
                                    code: code,
                                    timestamp: new Date().toISOString()
                                });
                                colorLog("[🎉] 成功捕获微信Code: " + code, 'green');
                                colorLog("[💾] 已保存到hookData.wxCodes", 'blue');
                            }
                        } catch (e) {
                            colorLog("[-] 提取Code失败: " + e, 'red');
                        }
                        originalCallback.onSuccess(result);
                    },
                    onError: function(error) {
                        colorLog("[-] 微信登录失败: " + error, 'red');
                        originalCallback.onError(error);
                    }
                }
            });
            
            return this.login(params, hookCallback.$new());
        };
    } catch (e) {
        colorLog("[-] Hook微信登录失败: " + e, 'red');
    }
    
    // ==================== 2. Hook云函数调用 ====================
    try {
        var CloudContainer = Java.use("com.tencent.mm.plugin.appbrand.jsapi.cloud.JsApiCallContainer");
        colorLog("[✓] 找到云函数容器类", 'green');
        
        CloudContainer.invoke.implementation = function(params) {
            var paramsStr = params.toString();
            
            // 检查是否是Nike相关的云函数调用
            if (paramsStr.includes("nike") || 
                paramsStr.includes("redeem") || 
                paramsStr.includes("complete_daily_sign") ||
                paramsStr.includes("wx096c43d1829a7788")) {
                
                colorLog("\n[☁️] 检测到Nike云函数调用", 'cyan');
                colorLog("参数: " + paramsStr, 'yellow');
                
                hookData.cloudCalls.push({
                    params: paramsStr,
                    timestamp: new Date().toISOString()
                });
            }
            
            var result = this.invoke(params);
            
            // 检查返回结果
            try {
                var resultStr = result.toString();
                if (resultStr.includes("points") || 
                    resultStr.includes("积分") || 
                    resultStr.includes("accessToken")) {
                    colorLog("[📥] 云函数返回: " + resultStr, 'magenta');
                }
            } catch (e) {
                // 忽略解析错误
            }
            
            return result;
        };
    } catch (e) {
        colorLog("[-] Hook云函数失败: " + e, 'red');
    }
    
    // ==================== 3. Hook HTTP网络请求 ====================
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        colorLog("[✓] 找到OkHttp客户端", 'green');
        
        OkHttpClient.newCall.implementation = function(request) {
            var url = request.url().toString();
            var method = request.method();
            
            // 只关注Nike相关请求
            if (url.includes("nike.com") || 
                url.includes("wechat") && url.includes("nike")) {
                
                colorLog("\n[🌐] HTTP请求: " + method + " " + url, 'cyan');
                
                // 获取请求头
                var headers = {};
                try {
                    var headerNames = request.headers().names();
                    var iterator = headerNames.iterator();
                    while (iterator.hasNext()) {
                        var name = iterator.next();
                        headers[name] = request.headers().get(name);
                    }
                } catch (e) {
                    colorLog("[-] 获取请求头失败: " + e, 'red');
                }
                
                // 保存重要请求
                if (url.includes("wechat_auth/token") || 
                    url.includes("complete_daily_sign") ||
                    url.includes("redeem")) {
                    
                    var requestData = {
                        url: url,
                        method: method,
                        headers: headers,
                        timestamp: new Date().toISOString()
                    };
                    
                    if (url.includes("wechat_auth/token")) {
                        colorLog("[🔐] Nike登录请求", 'yellow');
                        hookData.httpRequests.push(requestData);
                    } else if (url.includes("complete_daily_sign")) {
                        colorLog("[🎯] 签到请求!", 'green');
                        hookData.signInRequests.push(requestData);
                    }
                    
                    colorLog("Headers: " + JSON.stringify(headers, null, 2), 'blue');
                }
            }
            
            var call = this.newCall(request);
            
            // Hook响应
            try {
                var originalExecute = call.execute;
                call.execute.implementation = function() {
                    var response = originalExecute.call(this);
                    var requestUrl = request.url().toString();
                    
                    if (requestUrl.includes("nike.com")) {
                        try {
                            var responseBody = response.body().string();
                            
                            if (requestUrl.includes("wechat_auth/token")) {
                                colorLog("[🔐] Nike登录响应: " + responseBody, 'green');
                                if (responseBody.includes("accessToken")) {
                                    hookData.nikeTokens.push({
                                        response: responseBody,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } else if (requestUrl.includes("complete_daily_sign")) {
                                colorLog("[🎉] 签到响应: " + responseBody, 'green');
                            }
                            
                            // 重建响应体
                            var newBody = Java.use("okhttp3.ResponseBody").create(
                                response.body().contentType(),
                                responseBody
                            );
                            response = response.newBuilder().body(newBody).build();
                            
                        } catch (e) {
                            colorLog("[-] 处理响应失败: " + e, 'red');
                        }
                    }
                    
                    return response;
                };
            } catch (e) {
                colorLog("[-] Hook响应失败: " + e, 'red');
            }
            
            return call;
        };
    } catch (e) {
        colorLog("[-] Hook HTTP请求失败: " + e, 'red');
    }
    
    // ==================== 4. Hook小程序网络请求 ====================
    try {
        var AppBrandNetworkManager = Java.use("com.tencent.mm.plugin.appbrand.network.AppBrandNetworkManager");
        colorLog("[✓] 找到小程序网络管理器", 'green');
        
        AppBrandNetworkManager.request.overload(
            'java.lang.String', 
            'java.lang.String', 
            'java.util.Map', 
            'byte[]', 
            'com.tencent.mm.plugin.appbrand.network.AppBrandNetworkCallback'
        ).implementation = function(url, method, headers, data, callback) {
            
            if (url.includes("nike") || url.includes("redeem") || url.includes("sign")) {
                colorLog("\n[📱] 小程序请求: " + method + " " + url, 'cyan');
                
                if (headers) {
                    colorLog("Headers: " + headers.toString(), 'blue');
                }
                
                if (data) {
                    try {
                        var dataStr = Java.use("java.lang.String").$new(data);
                        colorLog("Data: " + dataStr, 'yellow');
                    } catch (e) {
                        colorLog("Data: [Binary " + data.length + " bytes]", 'yellow');
                    }
                }
            }
            
            return this.request(url, method, headers, data, callback);
        };
    } catch (e) {
        colorLog("[-] Hook小程序网络请求失败: " + e, 'red');
    }
    
    colorLog("\n[✅] Hook初始化完成!", 'green');
    colorLog("[💡] 请在手机上打开Nike小程序并执行签到操作", 'yellow');
    colorLog("[📊] 输入 'summary()' 查看捕获数据摘要", 'blue');
    colorLog("[💾] 输入 'export()' 导出所有数据", 'blue');
    colorLog("=====================================\n", 'white');
});

// 全局函数 - 显示数据摘要
global.summary = function() {
    colorLog("\n📊 数据捕获摘要", 'cyan');
    colorLog("=====================================", 'white');
    colorLog("微信Code数量: " + hookData.wxCodes.length, 'green');
    colorLog("Nike Token数量: " + hookData.nikeTokens.length, 'green');
    colorLog("签到请求数量: " + hookData.signInRequests.length, 'green');
    colorLog("云函数调用数量: " + hookData.cloudCalls.length, 'green');
    colorLog("HTTP请求数量: " + hookData.httpRequests.length, 'green');
    colorLog("=====================================\n", 'white');
    
    if (hookData.wxCodes.length > 0) {
        colorLog("最新微信Code: " + hookData.wxCodes[hookData.wxCodes.length - 1].code, 'yellow');
    }
    
    if (hookData.nikeTokens.length > 0) {
        colorLog("最新Nike Token: " + hookData.nikeTokens[hookData.nikeTokens.length - 1].response.substring(0, 100) + "...", 'yellow');
    }
};

// 全局函数 - 导出数据
global.exportData = function() {
    colorLog("\n💾 导出所有捕获数据", 'cyan');
    colorLog("=====================================", 'white');
    console.log(JSON.stringify(hookData, null, 2));
    colorLog("=====================================\n", 'white');
    return hookData;
};

// 全局函数 - 获取最新的微信Code
global.getLatestWxCode = function() {
    if (hookData.wxCodes.length > 0) {
        var latest = hookData.wxCodes[hookData.wxCodes.length - 1];
        colorLog("最新微信Code: " + latest.code, 'green');
        return latest.code;
    } else {
        colorLog("暂无微信Code", 'red');
        return null;
    }
};

// 全局函数 - 获取最新的Nike Token
global.getLatestNikeToken = function() {
    if (hookData.nikeTokens.length > 0) {
        var latest = hookData.nikeTokens[hookData.nikeTokens.length - 1];
        colorLog("最新Nike Token响应:", 'green');
        console.log(latest.response);
        return latest.response;
    } else {
        colorLog("暂无Nike Token", 'red');
        return null;
    }
};

// 定期显示状态
setInterval(function() {
    if (hookData.wxCodes.length > 0 || hookData.nikeTokens.length > 0 || 
        hookData.signInRequests.length > 0 || hookData.cloudCalls.length > 0) {
        colorLog("\n[📈] 实时状态 - Code:" + hookData.wxCodes.length + 
                " Token:" + hookData.nikeTokens.length + 
                " 签到:" + hookData.signInRequests.length + 
                " 云函数:" + hookData.cloudCalls.length, 'blue');
    }
}, 30000);

colorLog("🎯 脚本加载完成，等待Hook触发...", 'green');
