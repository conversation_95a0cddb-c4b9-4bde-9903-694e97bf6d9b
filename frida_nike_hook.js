/**
 * Nike小程序Frida Hook脚本
 * 用于捕获签到相关的云函数调用和网络请求
 * 作者：Tianxx
 * 版本：1.0
 */

console.log("[+] Nike小程序Hook脚本已加载");

// 全局变量存储捕获的数据
var capturedData = {
    wxCode: null,
    nikeToken: null,
    signInRequests: [],
    cloudCalls: []
};

Java.perform(function() {
    console.log("[+] 开始Hook微信小程序相关方法...");
    
    // ==================== 1. Hook微信登录获取code ====================
    try {
        var WXLogin = Java.use("com.tencent.mm.plugin.appbrand.jsapi.auth.JsApiLogin");
        console.log("[+] 找到微信登录类: JsApiLogin");
        
        WXLogin.login.implementation = function(params, callback) {
            console.log("[🔑] 微信登录调用");
            console.log("登录参数:", JSON.stringify(params));
            
            var self = this;
            var originalCallback = callback;
            
            // 创建新的回调来捕获code
            var newCallback = Java.registerClass({
                name: "com.nike.hook.LoginCallback",
                implements: [Java.use("com.tencent.mm.plugin.appbrand.jsapi.auth.LoginCallback")],
                methods: {
                    onSuccess: function(result) {
                        try {
                            var code = result.get("code");
                            capturedData.wxCode = code;
                            console.log("[✅] 捕获微信Code:", code);
                            
                            // 保存到文件
                            saveDataToFile("wxcode", code);
                            
                        } catch (e) {
                            console.log("[-] 提取code失败:", e);
                        }
                        
                        // 调用原始回调
                        originalCallback.onSuccess(result);
                    },
                    onError: function(error) {
                        console.log("[-] 微信登录失败:", error);
                        originalCallback.onError(error);
                    }
                }
            });
            
            return this.login(params, newCallback.$new());
        };
    } catch (e) {
        console.log("[-] Hook微信登录失败:", e);
    }
    
    // ==================== 2. Hook云函数调用 ====================
    try {
        var CloudContainer = Java.use("com.tencent.mm.plugin.appbrand.jsapi.cloud.JsApiCallContainer");
        console.log("[+] 找到云函数容器类: JsApiCallContainer");
        
        CloudContainer.invoke.implementation = function(params) {
            console.log("[☁️] 云函数调用");
            
            try {
                var paramsStr = params.toString();
                console.log("云函数参数:", paramsStr);
                
                // 检查是否是签到相关的调用
                if (paramsStr.includes("complete_daily_sign") || 
                    paramsStr.includes("redeem") || 
                    paramsStr.includes("sign")) {
                    console.log("[🎯] 发现签到相关云函数调用!");
                    
                    // 保存云函数调用数据
                    var cloudCallData = {
                        timestamp: new Date().toISOString(),
                        params: paramsStr,
                        type: "cloud_container"
                    };
                    capturedData.cloudCalls.push(cloudCallData);
                    saveDataToFile("cloud_calls", JSON.stringify(cloudCallData, null, 2));
                }
            } catch (e) {
                console.log("[-] 解析云函数参数失败:", e);
            }
            
            var result = this.invoke(params);
            
            try {
                var resultStr = result.toString();
                console.log("云函数返回:", resultStr);
                
                // 如果是签到相关的返回结果
                if (resultStr.includes("points") || resultStr.includes("积分")) {
                    console.log("[🎉] 捕获签到结果:", resultStr);
                    saveDataToFile("signin_result", resultStr);
                }
            } catch (e) {
                console.log("[-] 解析云函数返回失败:", e);
            }
            
            return result;
        };
    } catch (e) {
        console.log("[-] Hook云函数失败:", e);
    }
    
    // ==================== 3. Hook HTTP网络请求 ====================
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Request = Java.use("okhttp3.Request");
        
        console.log("[+] 找到OkHttp类");
        
        OkHttpClient.newCall.implementation = function(request) {
            var url = request.url().toString();
            var method = request.method();
            
            // 只关注Nike相关的请求
            if (url.includes("nike.com") || url.includes("wechat")) {
                console.log("[🌐] HTTP请求:", method, url);
                
                // 获取请求头
                var headers = {};
                var headerNames = request.headers().names();
                var headerIterator = headerNames.iterator();
                while (headerIterator.hasNext()) {
                    var name = headerIterator.next();
                    var value = request.headers().get(name);
                    headers[name] = value;
                }
                
                // 检查是否是签到相关请求
                if (url.includes("complete_daily_sign") || 
                    url.includes("redeem") ||
                    url.includes("wechat_auth/token")) {
                    
                    console.log("[🎯] 发现关键请求!");
                    console.log("URL:", url);
                    console.log("Method:", method);
                    console.log("Headers:", JSON.stringify(headers, null, 2));
                    
                    // 保存请求数据
                    var requestData = {
                        timestamp: new Date().toISOString(),
                        url: url,
                        method: method,
                        headers: headers,
                        type: "http_request"
                    };
                    
                    if (url.includes("wechat_auth/token")) {
                        saveDataToFile("nike_login", JSON.stringify(requestData, null, 2));
                    } else if (url.includes("complete_daily_sign")) {
                        capturedData.signInRequests.push(requestData);
                        saveDataToFile("signin_request", JSON.stringify(requestData, null, 2));
                    }
                }
            }
            
            var call = this.newCall(request);
            
            // Hook响应
            try {
                var originalExecute = call.execute;
                call.execute.implementation = function() {
                    var response = originalExecute.call(this);
                    
                    var requestUrl = request.url().toString();
                    if (requestUrl.includes("nike.com")) {
                        try {
                            var responseBody = response.body().string();
                            console.log("[📥] 响应内容:", responseBody);
                            
                            // 如果是Nike token响应
                            if (requestUrl.includes("wechat_auth/token") && responseBody.includes("accessToken")) {
                                console.log("[🔐] 捕获Nike Token响应!");
                                capturedData.nikeToken = responseBody;
                                saveDataToFile("nike_token", responseBody);
                            }
                            
                            // 如果是签到响应
                            if (requestUrl.includes("complete_daily_sign")) {
                                console.log("[🎉] 捕获签到响应!");
                                saveDataToFile("signin_response", responseBody);
                            }
                            
                            // 重新创建响应体
                            var newBody = Java.use("okhttp3.ResponseBody").create(
                                response.body().contentType(),
                                responseBody
                            );
                            
                            var responseBuilder = response.newBuilder();
                            responseBuilder.body(newBody);
                            response = responseBuilder.build();
                            
                        } catch (e) {
                            console.log("[-] 处理响应失败:", e);
                        }
                    }
                    
                    return response;
                };
            } catch (e) {
                console.log("[-] Hook响应失败:", e);
            }
            
            return call;
        };
    } catch (e) {
        console.log("[-] Hook HTTP请求失败:", e);
    }
    
    // ==================== 4. Hook微信小程序网络请求 ====================
    try {
        var WXNetworkManager = Java.use("com.tencent.mm.plugin.appbrand.network.AppBrandNetworkManager");
        console.log("[+] 找到微信小程序网络管理类");
        
        WXNetworkManager.request.overload('java.lang.String', 'java.lang.String', 'java.util.Map', 'byte[]', 'com.tencent.mm.plugin.appbrand.network.AppBrandNetworkCallback').implementation = function(url, method, headers, data, callback) {
            
            if (url.includes("nike") || url.includes("redeem") || url.includes("sign")) {
                console.log("[📱] 小程序网络请求:");
                console.log("URL:", url);
                console.log("Method:", method);
                console.log("Headers:", headers ? headers.toString() : "null");
                
                if (data) {
                    try {
                        var dataStr = Java.use("java.lang.String").$new(data);
                        console.log("Data:", dataStr);
                    } catch (e) {
                        console.log("Data: [Binary Data]");
                    }
                }
                
                // 保存小程序请求数据
                var mpRequestData = {
                    timestamp: new Date().toISOString(),
                    url: url,
                    method: method,
                    headers: headers ? headers.toString() : null,
                    type: "miniprogram_request"
                };
                saveDataToFile("mp_request", JSON.stringify(mpRequestData, null, 2));
            }
            
            return this.request(url, method, headers, data, callback);
        };
    } catch (e) {
        console.log("[-] Hook小程序网络请求失败:", e);
    }
    
    // ==================== 辅助函数 ====================
    function saveDataToFile(filename, data) {
        try {
            var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            var fullFilename = "/sdcard/nike_hook_" + filename + "_" + timestamp + ".txt";
            
            var File = Java.use("java.io.File");
            var FileWriter = Java.use("java.io.FileWriter");
            
            var file = File.$new(fullFilename);
            var writer = FileWriter.$new(file);
            writer.write(data);
            writer.close();
            
            console.log("[💾] 数据已保存到:", fullFilename);
        } catch (e) {
            console.log("[-] 保存文件失败:", e);
        }
    }
    
    console.log("[✅] Hook脚本初始化完成!");
    console.log("[💡] 现在请在微信中打开Nike小程序并执行签到操作");
});

// 定期输出捕获的数据摘要
setInterval(function() {
    console.log("\n[📊] 数据捕获摘要:");
    console.log("微信Code:", capturedData.wxCode ? "已捕获" : "未捕获");
    console.log("Nike Token:", capturedData.nikeToken ? "已捕获" : "未捕获");
    console.log("签到请求数量:", capturedData.signInRequests.length);
    console.log("云函数调用数量:", capturedData.cloudCalls.length);
    console.log("─".repeat(50));
}, 30000); // 每30秒输出一次摘要
