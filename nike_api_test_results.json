{"directHttp": [{"api": "/onemp/user/profile/v1", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35E6933F9133326DE483</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/user/info/v1", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35EBFB200F3032DFBC91</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/redeem_center_info/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35F089F0063838032E5E</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/user_task/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35F4598BE13835307EFF</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/recent_actions/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35F99670D234318C4FF8</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/complete_daily_sign/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B35FDA73E4C353747C596</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/stroll_task_info/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B36023D204E3031CCD898</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}, {"api": "/onemp/redeem/complete_stroll_task/v2", "result": {"success": false, "status": 404, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Error>\n  <Code>NoSuchBucket</Code>\n  <Message>The specified bucket does not exist.</Message>\n  <RequestId>688B36067F5FD1323895EE63</RequestId>\n  <HostId>api.nike.com.cn</HostId>\n  <BucketName>onemp</BucketName>\n  <EC>0015-00000101</EC>\n  <RecommendDoc>https://api.aliyun.com/troubleshoot?q=0015-00000101</RecommendDoc>\n</Error>\n"}}], "cloudProxy": [{"api": "/onemp/user/profile/v1", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/user/info/v1", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/redeem_center_info/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/user_task/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/recent_actions/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/complete_daily_sign/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/stroll_task_info/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}, {"api": "/onemp/redeem/complete_stroll_task/v2", "result": {"success": false, "status": 403, "data": {"message": "Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): 'WGw/1eeDoHMDtz9VTm4pEBBE7qBseS0tOb7MQvKN+BA='."}}}], "wxcodeCloud": [{"api": "/onemp/user/profile/v1", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/user/info/v1", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/redeem_center_info/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/user_task/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/recent_actions/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/complete_daily_sign/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/stroll_task_info/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}, {"api": "/onemp/redeem/complete_stroll_task/v2", "result": {"success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined"}}]}