{"name": "nike-wechat-signin", "version": "2.0.0", "description": "Nike小程序自动签到脚本", "main": "nike.js", "scripts": {"start": "node nike.js", "test": "node test_nike.js", "debug": "node nike.js --debug"}, "keywords": ["nike", "wechat", "miniprogram", "signin", "automation"], "author": "Tianxx", "license": "MIT", "dependencies": {"request": "^2.88.2"}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/nike-wechat-signin.git"}, "bugs": {"url": "https://github.com/your-username/nike-wechat-signin/issues"}, "homepage": "https://github.com/your-username/nike-wechat-signin#readme"}