/**
 * 简化的iOS Hook测试脚本
 * 用于验证Frida是否能正常工作
 */

console.log("🧪 开始iOS Hook测试...");

if (ObjC.available) {
    console.log("✅ Objective-C运行时可用");
    
    // 1. 测试基本的NSLog Hook
    try {
        var NSLog = new NativeFunction(Module.findExportByName('Foundation', 'NSLog'), 'void', ['pointer', '...']);
        console.log("✅ 找到NSLog函数");
    } catch (e) {
        console.log("❌ NSLog Hook失败:", e);
    }
    
    // 2. Hook所有网络请求
    try {
        var NSURLSession = ObjC.classes.NSURLSession;
        if (NSURLSession) {
            console.log("✅ 找到NSURLSession");
            
            // Hook dataTaskWithRequest
            var originalMethod = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            if (originalMethod) {
                console.log("✅ 找到dataTaskWithRequest方法");
                
                Interceptor.attach(originalMethod.implementation, {
                    onEnter: function(args) {
                        try {
                            var request = new ObjC.Object(args[2]);
                            var url = request.URL().absoluteString().toString();
                            console.log("🌐 网络请求:", url);
                            
                            // 特别关注Nike相关请求
                            if (url.includes('nike') || url.includes('wechat')) {
                                console.log("🎯 发现目标请求:", url);
                            }
                        } catch (e) {
                            console.log("解析请求失败:", e);
                        }
                    }
                });
            }
        }
    } catch (e) {
        console.log("❌ NSURLSession Hook失败:", e);
    }
    
    // 3. Hook NSString的创建来监控字符串
    try {
        var NSString = ObjC.classes.NSString;
        if (NSString) {
            var stringWithUTF8String = NSString['+ stringWithUTF8String:'];
            if (stringWithUTF8String) {
                Interceptor.attach(stringWithUTF8String.implementation, {
                    onEnter: function(args) {
                        try {
                            var str = Memory.readUtf8String(args[2]);
                            if (str && (str.includes('nike') || str.includes('complete_daily_sign') || str.includes('redeem'))) {
                                console.log("🔍 发现关键字符串:", str);
                            }
                        } catch (e) {
                            // 忽略
                        }
                    }
                });
            }
        }
    } catch (e) {
        console.log("❌ NSString Hook失败:", e);
    }
    
    // 4. 通用的方法调用监控
    try {
        // 获取所有类名
        var classes = Object.keys(ObjC.classes);
        var wechatClasses = classes.filter(name => 
            name.toLowerCase().includes('wechat') || 
            name.toLowerCase().includes('wx') ||
            name.toLowerCase().includes('miniprogram') ||
            name.toLowerCase().includes('appbrand')
        );
        
        console.log("📋 找到微信相关类:", wechatClasses.length, "个");
        wechatClasses.slice(0, 10).forEach(className => {
            console.log("  -", className);
        });
        
    } catch (e) {
        console.log("❌ 类枚举失败:", e);
    }
    
    console.log("\n✅ Hook测试脚本初始化完成");
    console.log("💡 现在请在iPhone上操作Nike小程序");
    console.log("📱 任何网络请求都会被记录");
    
} else {
    console.log("❌ Objective-C运行时不可用");
}

// 全局测试函数
this.testHook = function() {
    console.log("🧪 执行Hook测试...");
    
    // 手动触发一个网络请求来测试
    try {
        var url = ObjC.classes.NSURL.URLWithString_("https://www.apple.com");
        var request = ObjC.classes.NSURLRequest.requestWithURL_(url);
        var session = ObjC.classes.NSURLSession.sharedSession();
        
        console.log("🔄 发送测试请求...");
        session.dataTaskWithRequest_completionHandler_(request, new ObjC.Block({
            retType: 'void',
            argTypes: ['object', 'object', 'object'],
            implementation: function(data, response, error) {
                console.log("📥 测试请求完成");
            }
        })).resume();
        
    } catch (e) {
        console.log("❌ 测试请求失败:", e);
    }
};

this.listClasses = function() {
    var classes = Object.keys(ObjC.classes);
    var filtered = classes.filter(name => 
        name.toLowerCase().includes('wechat') || 
        name.toLowerCase().includes('wx') ||
        name.toLowerCase().includes('nike') ||
        name.toLowerCase().includes('http') ||
        name.toLowerCase().includes('url')
    );
    
    console.log("🔍 相关类列表:");
    filtered.forEach(name => console.log("  -", name));
    return filtered;
};

console.log("🎯 测试脚本加载完成，输入 testHook() 或 listClasses() 进行测试");
