/**
 * Nike小程序专业签到脚本
 * 基于深度源码分析和抓包数据
 * 支持多种签到方式
 * 作者：Tianxx
 * 版本：4.0
 */

const axios = require('axios');
const fs = require('fs');

// 从抓包和源码分析得到的完整配置
const NIKE_CONFIG = {
    // 基础配置
    wxid: 'wxid_ltkystdcspc822',
    appId: 'wx096c43d1829a7788',
    phoneNumber: '+*************',
    
    // Nike API配置
    nikeApiBaseUrl: 'https://api.nike.com.cn',
    wechatApiUrl: 'https://wechat.nike.com.cn', 
    accountsUrl: 'https://accounts.nike.com.cn',
    
    // 客户端配置 (从抓包数据获取)
    clientId: 'b75a7f09c6f0a046a73b97532ac971b',
    redirectUri: 'https://mp-static-assets.gc.nike.com/auth/wechat-shop/index.html',
    
    // 云开发配置 (从config.js获取)
    wxCloud: {
        envId: "test-52toys-7gc5xgy7a72c959f",
        excludeCredentials: "cloudbase-access-token",
        gatewayId: "test-8g5tq0ha6215e83c"
    },
    
    // 签到API路径
    signInPath: '/onemp/redeem/complete_daily_sign/v2',
    
    // 请求头配置
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json; charset=UTF-8',
        'nike-api-caller-id': 'nike:wechat:web:1.0',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003D57) NetType/WIFI Language/zh_CN',
        'App-Id': 'wechat:mp:wx096c43d1829a7788'
    }
};

// 日志函数
const log = (message, type = 'info') => {
    const timestamp = new Date().toLocaleString();
    const colors = {
        info: '\x1b[37m',    // 白色
        success: '\x1b[32m', // 绿色
        warning: '\x1b[33m', // 黄色
        error: '\x1b[31m',   // 红色
        debug: '\x1b[36m',   // 青色
        reset: '\x1b[0m'
    };
    
    console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
};

// 加载认证信息
function loadAuthInfo() {
    try {
        if (fs.existsSync('./nike_tokens.json')) {
            const data = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
            log('✅ 成功加载认证信息', 'success');
            return data;
        } else {
            log('❌ 未找到nike_tokens.json文件', 'error');
            return null;
        }
    } catch (error) {
        log(`❌ 读取认证信息失败: ${error.message}`, 'error');
        return null;
    }
}

// 方案1: 直接HTTP请求 (最简单)
async function directHttpSignIn(accessToken, userId) {
    log('🎯 方案1: 直接HTTP请求签到', 'info');
    
    const url = `${NIKE_CONFIG.nikeApiBaseUrl}${NIKE_CONFIG.signInPath}/${userId}`;
    const headers = {
        ...NIKE_CONFIG.headers,
        'Authorization': `Bearer ${accessToken}`,
        'HOST': 'api.nike.com.cn'
    };
    
    log(`📡 请求URL: ${url}`, 'debug');
    log(`🔑 Authorization: Bearer ${accessToken.substring(0, 20)}...`, 'debug');
    
    try {
        const response = await axios.get(url, { 
            headers,
            timeout: 10000,
            validateStatus: function (status) {
                return status < 500; // 接受所有小于500的状态码
            }
        });
        
        log(`📊 响应状态: ${response.status}`, 'debug');
        log(`📥 响应数据: ${JSON.stringify(response.data)}`, 'debug');
        
        if (response.status === 200 && response.data) {
            const points = response.data.points || 0;
            log(`🎉 签到成功! 获得 ${points} 积分`, 'success');
            return { success: true, points, data: response.data };
        } else {
            log(`⚠️ 签到响应异常: ${response.status}`, 'warning');
            return { success: false, status: response.status, data: response.data };
        }
    } catch (error) {
        log(`❌ HTTP请求失败: ${error.message}`, 'error');
        if (error.response) {
            log(`📊 错误状态: ${error.response.status}`, 'error');
            log(`📊 错误数据: ${JSON.stringify(error.response.data)}`, 'error');
        }
        return { success: false, error: error.message };
    }
}

// 方案2: 模拟云函数调用
async function cloudContainerSignIn(accessToken, userId) {
    log('🎯 方案2: 模拟云函数调用签到', 'info');
    
    // 构造云函数调用参数 (基于HttpClient.js源码)
    const containerParams = {
        path: `${NIKE_CONFIG.signInPath}/${userId}`,
        method: "GET",
        header: {
            "X-WX-EXCLUDE-CREDENTIALS": NIKE_CONFIG.wxCloud.excludeCredentials,
            "X-WX-GATEWAY-ID": NIKE_CONFIG.wxCloud.gatewayId,
            "HOST": "api.nike.com.cn",
            "Authorization": `Bearer ${accessToken}`,
            ...NIKE_CONFIG.headers
        }
    };
    
    log('📋 云函数参数:', 'debug');
    log(JSON.stringify(containerParams, null, 2), 'debug');
    
    // 尝试通过微信云开发代理
    const proxyUrl = `${NIKE_CONFIG.wechatApiUrl}/proxy-v2${NIKE_CONFIG.signInPath}/${userId}`;
    const proxyHeaders = {
        ...NIKE_CONFIG.headers,
        'Authorization': `Bearer ${accessToken}`,
        'X-WX-EXCLUDE-CREDENTIALS': NIKE_CONFIG.wxCloud.excludeCredentials,
        'X-WX-GATEWAY-ID': NIKE_CONFIG.wxCloud.gatewayId,
        'HOST': 'wechat.nike.com.cn'
    };
    
    try {
        const response = await axios.get(proxyUrl, { 
            headers: proxyHeaders,
            timeout: 15000,
            validateStatus: function (status) {
                return status < 500;
            }
        });
        
        log(`📊 代理响应状态: ${response.status}`, 'debug');
        log(`📥 代理响应数据: ${JSON.stringify(response.data)}`, 'debug');
        
        if (response.status === 200 && response.data) {
            const points = response.data.points || 0;
            log(`🎉 云函数签到成功! 获得 ${points} 积分`, 'success');
            return { success: true, points, data: response.data };
        } else {
            log(`⚠️ 云函数签到响应异常: ${response.status}`, 'warning');
            return { success: false, status: response.status, data: response.data };
        }
    } catch (error) {
        log(`❌ 云函数调用失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

// 方案3: 使用wxcode.js的云函数接口
async function wxcodeCloudSignIn(accessToken, userId) {
    log('🎯 方案3: 使用wxcode云函数接口签到', 'info');
    
    try {
        const wxcode = require('./wxcode');
        
        // 构造云函数调用数据
        const cloudFunctionData = {
            action: 'nike_sign_in',
            method: 'GET',
            url: `${NIKE_CONFIG.signInPath}/${userId}`,
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'App-Id': NIKE_CONFIG.appId,
                'nike-api-caller-id': 'nike:wechat:web:1.0'
            },
            envId: NIKE_CONFIG.wxCloud.envId
        };
        
        log('📋 云函数数据:', 'debug');
        log(JSON.stringify(cloudFunctionData, null, 2), 'debug');
        
        const result = await wxcode.getUserInfo(
            NIKE_CONFIG.wxid,
            NIKE_CONFIG.appId,
            JSON.stringify(cloudFunctionData)
        );
        
        if (result.success) {
            log('✅ wxcode云函数调用成功', 'success');
            log(`📥 返回数据: ${JSON.stringify(result.rawData)}`, 'debug');
            
            try {
                const signInResult = JSON.parse(result.rawData);
                if (signInResult.points !== undefined) {
                    log(`🎉 wxcode签到成功! 获得 ${signInResult.points} 积分`, 'success');
                    return { success: true, points: signInResult.points, data: signInResult };
                } else {
                    return { success: true, data: signInResult };
                }
            } catch (e) {
                return { success: true, data: result.rawData };
            }
        } else {
            log(`❌ wxcode云函数调用失败: ${result.error}`, 'error');
            return { success: false, error: result.error };
        }
    } catch (error) {
        log(`❌ wxcode模块加载失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

// 生成完整的API调用信息
function generateAPIInfo(accessToken, userId) {
    log('\n📋 完整的Nike签到API信息', 'info');
    log('=====================================', 'info');
    
    // 直接HTTP调用
    log('🔗 方案1 - 直接HTTP调用:', 'info');
    log(`URL: ${NIKE_CONFIG.nikeApiBaseUrl}${NIKE_CONFIG.signInPath}/${userId}`, 'debug');
    log(`Method: GET`, 'debug');
    log(`Authorization: Bearer ${accessToken.substring(0, 30)}...`, 'debug');
    
    // 云函数调用
    log('\n🌐 方案2 - 云函数调用:', 'info');
    const containerParams = {
        path: `${NIKE_CONFIG.signInPath}/${userId}`,
        method: "GET",
        header: {
            "X-WX-EXCLUDE-CREDENTIALS": NIKE_CONFIG.wxCloud.excludeCredentials,
            "X-WX-GATEWAY-ID": NIKE_CONFIG.wxCloud.gatewayId,
            "HOST": "api.nike.com.cn",
            "Authorization": `Bearer ${accessToken}`,
            "App-Id": NIKE_CONFIG.appId
        }
    };
    log(`wx.cloud.callContainer(${JSON.stringify(containerParams, null, 2)})`, 'debug');
    
    // curl命令
    log('\n📋 curl命令:', 'info');
    log(`curl -X GET \\
  "${NIKE_CONFIG.nikeApiBaseUrl}${NIKE_CONFIG.signInPath}/${userId}" \\
  -H "Authorization: Bearer ${accessToken}" \\
  -H "App-Id: ${NIKE_CONFIG.appId}" \\
  -H "nike-api-caller-id: nike:wechat:web:1.0" \\
  -H "Accept: application/json"`, 'debug');
    
    log('=====================================\n', 'info');
}

// 主函数
async function main() {
    log('🚀 Nike专业签到脚本 v4.0', 'info');
    log('=====================================', 'info');
    
    // 1. 加载认证信息
    const authInfo = loadAuthInfo();
    if (!authInfo) {
        log('❌ 无法获取认证信息，请先获取Nike Token', 'error');
        log('💡 提示: 请确保nike_tokens.json文件存在并包含有效的accessToken', 'warning');
        return;
    }
    
    const { accessToken, userId } = authInfo;
    const finalUserId = userId || authInfo.wxid || NIKE_CONFIG.wxid;
    
    log(`📋 用户ID: ${finalUserId}`, 'info');
    log(`🔑 Token: ${accessToken ? accessToken.substring(0, 20) + '...' : '未找到'}`, 'info');
    
    if (!accessToken) {
        log('❌ 缺少accessToken，无法进行签到', 'error');
        return;
    }
    
    // 2. 生成API信息
    generateAPIInfo(accessToken, finalUserId);
    
    // 3. 尝试多种签到方案
    const methods = [
        { name: '直接HTTP请求', func: directHttpSignIn },
        { name: '云函数代理', func: cloudContainerSignIn },
        { name: 'wxcode云函数', func: wxcodeCloudSignIn }
    ];
    
    for (const method of methods) {
        log(`\n🔄 尝试${method.name}...`, 'info');
        
        try {
            const result = await method.func(accessToken, finalUserId);
            
            if (result.success) {
                log(`✅ ${method.name}成功!`, 'success');
                if (result.points) {
                    log(`🎉 获得 ${result.points} 积分`, 'success');
                }
                log(`📊 完整响应: ${JSON.stringify(result.data)}`, 'debug');
                break; // 成功后退出循环
            } else {
                log(`❌ ${method.name}失败: ${result.error || '未知错误'}`, 'error');
            }
        } catch (error) {
            log(`❌ ${method.name}异常: ${error.message}`, 'error');
        }
        
        // 等待1秒后尝试下一个方案
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    log('\n🏁 签到脚本执行完成', 'info');
}

// 执行脚本
if (require.main === module) {
    main().catch(error => {
        log(`❌ 脚本执行出错: ${error.message}`, 'error');
        console.error(error.stack);
    });
}

module.exports = {
    directHttpSignIn,
    cloudContainerSignIn,
    wxcodeCloudSignIn,
    generateAPIInfo
};
