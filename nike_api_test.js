/**
 * Nike API测试脚本
 * 测试各种Nike API调用方式，找到正确的调用模式
 * 作者：Tianxx
 */

const axios = require('axios');
const fs = require('fs');

// 从小程序源码中提取的所有API端点
const NIKE_APIS = [
    // 用户相关
    '/onemp/user/profile/v1',
    '/onemp/user/info/v1',
    
    // 兑换中心相关
    '/onemp/redeem/redeem_center_info/v2',
    '/onemp/redeem/user_task/v2',
    '/onemp/redeem/recent_actions/v2',
    
    // 签到相关（用于对比）
    '/onemp/redeem/complete_daily_sign/v2',
    
    // 其他
    '/onemp/redeem/stroll_task_info/v2',
    '/onemp/redeem/complete_stroll_task/v2'
];

const CONFIG = {
    baseUrl: 'https://api.nike.com.cn',
    wechatUrl: 'https://wechat.nike.com.cn',
    appId: 'wx096c43d1829a7788',
    
    // 云开发配置
    cloudConfig: {
        envId: "test-52toys-7gc5xgy7a72c959f",
        excludeCredentials: "cloudbase-access-token",
        gatewayId: "test-8g5tq0ha6215e83c"
    }
};

// 加载认证信息
function loadAuth() {
    try {
        const data = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
        console.log(`✅ 加载认证信息: ${data.userId}`);
        return data;
    } catch (error) {
        console.log(`❌ 无法加载认证信息: ${error.message}`);
        return null;
    }
}

// 测试直接HTTP调用
async function testDirectHttp(apiPath, auth) {
    const url = `${CONFIG.baseUrl}${apiPath}`;
    const userId = auth.userId || auth.wxid;
    const finalUrl = apiPath.includes('{userId}') ? url.replace('{userId}', userId) : 
                     apiPath.endsWith('/v2') ? `${url}/${userId}` : url;
    
    console.log(`\n🔍 测试直接HTTP: ${finalUrl}`);
    
    try {
        const response = await axios.get(finalUrl, {
            headers: {
                'Authorization': `Bearer ${auth.accessToken}`,
                'App-Id': CONFIG.appId,
                'Accept': 'application/json',
                'nike-api-caller-id': 'nike:wechat:web:1.0'
            },
            timeout: 10000,
            validateStatus: () => true // 接受所有状态码
        });
        
        console.log(`📊 状态: ${response.status}`);
        if (response.status === 200) {
            console.log(`✅ 成功! 数据: ${JSON.stringify(response.data).substring(0, 100)}...`);
            return { success: true, data: response.data };
        } else {
            console.log(`❌ 失败: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
            return { success: false, status: response.status, data: response.data };
        }
    } catch (error) {
        console.log(`❌ 异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试云函数代理调用
async function testCloudProxy(apiPath, auth) {
    const userId = auth.userId || auth.wxid;
    const finalPath = apiPath.includes('{userId}') ? apiPath.replace('{userId}', userId) : 
                      apiPath.endsWith('/v2') ? `${apiPath}/${userId}` : apiPath;
    
    const proxyUrl = `${CONFIG.wechatUrl}/proxy${finalPath}`;
    
    console.log(`\n🌐 测试云函数代理: ${proxyUrl}`);
    
    try {
        const response = await axios.get(proxyUrl, {
            headers: {
                'Authorization': `Bearer ${auth.accessToken}`,
                'App-Id': CONFIG.appId,
                'X-WX-EXCLUDE-CREDENTIALS': CONFIG.cloudConfig.excludeCredentials,
                'X-WX-GATEWAY-ID': CONFIG.cloudConfig.gatewayId,
                'HOST': 'wechat.nike.com.cn',
                'Accept': 'application/json'
            },
            timeout: 10000,
            validateStatus: () => true
        });
        
        console.log(`📊 状态: ${response.status}`);
        if (response.status === 200) {
            console.log(`✅ 成功! 数据: ${JSON.stringify(response.data).substring(0, 100)}...`);
            return { success: true, data: response.data };
        } else {
            console.log(`❌ 失败: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
            return { success: false, status: response.status, data: response.data };
        }
    } catch (error) {
        console.log(`❌ 异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试wxcode云函数调用
async function testWxcodeCloud(apiPath, auth) {
    console.log(`\n☁️ 测试wxcode云函数: ${apiPath}`);
    
    try {
        const wxcode = require('./wxcode');
        const userId = auth.userId || auth.wxid;
        const finalPath = apiPath.includes('{userId}') ? apiPath.replace('{userId}', userId) : 
                          apiPath.endsWith('/v2') ? `${apiPath}/${userId}` : apiPath;
        
        const cloudData = {
            action: 'nike_api_call',
            path: finalPath,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${auth.accessToken}`,
                'App-Id': CONFIG.appId
            }
        };
        
        const result = await wxcode.getUserInfo(
            auth.wxid,
            CONFIG.appId,
            JSON.stringify(cloudData)
        );
        
        if (result.success) {
            console.log(`✅ 成功! 数据: ${JSON.stringify(result.rawData).substring(0, 100)}...`);
            return { success: true, data: result.rawData };
        } else {
            console.log(`❌ 失败: ${result.error}`);
            return { success: false, error: result.error };
        }
    } catch (error) {
        console.log(`❌ 异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 主测试函数
async function main() {
    console.log('🚀 Nike API测试脚本');
    console.log('目标: 找到正确的API调用方式');
    console.log('=====================================\n');
    
    const auth = loadAuth();
    if (!auth) {
        console.log('❌ 无法加载认证信息');
        return;
    }
    
    const results = {
        directHttp: [],
        cloudProxy: [],
        wxcodeCloud: []
    };
    
    // 测试每个API
    for (const apiPath of NIKE_APIS) {
        console.log(`\n🎯 测试API: ${apiPath}`);
        console.log('='.repeat(50));
        
        // 方案1: 直接HTTP
        const httpResult = await testDirectHttp(apiPath, auth);
        results.directHttp.push({ api: apiPath, result: httpResult });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 方案2: 云函数代理
        const proxyResult = await testCloudProxy(apiPath, auth);
        results.cloudProxy.push({ api: apiPath, result: proxyResult });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 方案3: wxcode云函数
        const wxcodeResult = await testWxcodeCloud(apiPath, auth);
        results.wxcodeCloud.push({ api: apiPath, result: wxcodeResult });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 汇总结果
    console.log('\n📊 测试结果汇总');
    console.log('=====================================');
    
    const successfulMethods = [];
    
    ['directHttp', 'cloudProxy', 'wxcodeCloud'].forEach(method => {
        const successes = results[method].filter(r => r.result.success);
        console.log(`${method}: ${successes.length}/${results[method].length} 成功`);
        
        if (successes.length > 0) {
            successfulMethods.push(method);
            console.log(`✅ ${method} 成功的API:`);
            successes.forEach(s => console.log(`  - ${s.api}`));
        }
    });
    
    if (successfulMethods.length > 0) {
        console.log(`\n🎉 找到可用的调用方式: ${successfulMethods.join(', ')}`);
        console.log('💡 现在可以用这种方式实现签到功能!');
    } else {
        console.log('\n❌ 所有调用方式都失败了');
        console.log('💡 可能需要更深入的逆向分析');
    }
    
    // 保存详细结果
    fs.writeFileSync('./nike_api_test_results.json', JSON.stringify(results, null, 2));
    console.log('\n💾 详细结果已保存到 nike_api_test_results.json');
}

// 执行测试
main().catch(error => {
    console.log(`❌ 测试失败: ${error.message}`);
    console.error(error.stack);
});
