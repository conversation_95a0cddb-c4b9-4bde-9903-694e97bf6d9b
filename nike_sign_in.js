/**
 * Nike小程序签到脚本
 * 基于小程序源码分析
 * 作者：Tianxx
 * 版本：1.0
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置信息
const CONFIG = {
    // 微信授权相关
    wxCode: '',  // 微信授权码，通过wxcode.js获取
    wxid: '',    // 微信ID
    
    // Nike API相关
    nikeApiBaseUrl: 'https://api.nike.com.cn',
    wechatApiUrl: 'https://wechat.nike.com.cn',
    accountsUrl: 'https://accounts.nike.com.cn',
    appId: 'wechat:mp:wx096c43d1829a7788',
    clientId: '5e02c316811ebcb9e6960bc4bdefdaf1',
    
    // 签到任务ID (从小程序源码中提取)
    signInTaskId: 'a5f7b892-fdfc-46f5-92e9-c0a6e8f9e37a',
    
    // 缓存文件
    tokenCacheFile: './nike_tokens.json',
    
    // 调试模式
    debug: true
};

// 认证信息
let AUTH = {
    accessToken: '',
    refreshToken: '',
    userId: '',
    expiresIn: 0,
    tokenTimestamp: 0
};

// 工具函数
const log = (message) => {
    console.log(message);
};

const debug = (message) => {
    if (CONFIG.debug) {
        console.log(`[DEBUG] ${message}`);
    }
};

// 加载缓存的Token
const loadTokenCache = () => {
    try {
        if (fs.existsSync(CONFIG.tokenCacheFile)) {
            const data = fs.readFileSync(CONFIG.tokenCacheFile, 'utf8');
            const cache = JSON.parse(data);
            
            // 检查缓存是否有效
            const now = Date.now();
            const tokenAge = now - cache.tokenTimestamp;
            const tokenValidityMs = (cache.expiresIn || 7200) * 1000;
            
            if (tokenAge < tokenValidityMs) {
                debug(`使用缓存的Token，有效期还剩${Math.round((tokenValidityMs - tokenAge) / 1000)}秒`);
                return cache;
            } else {
                debug(`缓存的Token已过期，需要重新获取`);
            }
        }
    } catch (error) {
        debug(`读取Token缓存失败: ${error.message}`);
    }
    return null;
};

// 保存Token到缓存
const saveTokenCache = () => {
    try {
        const cacheData = {
            ...AUTH,
            tokenTimestamp: Date.now()
        };
        fs.writeFileSync(CONFIG.tokenCacheFile, JSON.stringify(cacheData, null, 2));
        debug(`Token已保存到缓存`);
    } catch (error) {
        debug(`保存Token缓存失败: ${error.message}`);
    }
};

// 从JWT token中提取用户ID
const extractUserIdFromToken = (token) => {
    if (!token) return null;
    
    try {
        // JWT token格式: header.payload.signature
        const parts = token.split('.');
        if (parts.length !== 3) return null;
        
        // 解码payload部分
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        
        if (CONFIG.debug) {
            debug(`JWT Payload: ${JSON.stringify({
                openId: payload.openId,
                unionId: payload.unionId,
                sub: payload.sub,
                aud: payload.aud
            })}`);
        }
        
        // 尝试从不同字段获取用户ID
        return payload.openId || payload.unionId || payload.sub || null;
    } catch (error) {
        debug(`解析JWT token失败: ${error.message}`);
        return null;
    }
};

// Nike登录 - 使用微信code获取Nike Token
const nikeLogin = async () => {
    if (!CONFIG.wxCode) {
        log(`❌ 缺少微信授权码，无法进行Nike登录`);
        return false;
    }

    debug(`开始Nike登录...`);

    try {
        const response = await axios.post(`${CONFIG.wechatApiUrl}/wechat_auth/token/v1`, {
            appId: CONFIG.appId,
            code: CONFIG.wxCode
        });

        const data = response.data;
        if (data && (data.accessToken || data.access_token)) {
            AUTH.accessToken = data.accessToken || data.access_token;
            AUTH.refreshToken = data.refreshToken || data.refresh_token;
            AUTH.userId = data.user_id || data.upmId || extractUserIdFromToken(AUTH.accessToken);
            AUTH.expiresIn = data.expiresIn || data.expires_in || 7200;

            debug(`Nike登录成功`);
            debug(`User ID: ${AUTH.userId}`);
            debug(`Token过期时间: ${AUTH.expiresIn}秒`);

            // 保存Token到缓存
            saveTokenCache();
            return true;
        } else {
            log(`❌ Nike登录失败: 响应格式错误`);
            return false;
        }
    } catch (error) {
        log(`❌ Nike登录失败: ${error.message}`);
        if (error.response) {
            debug(`响应状态: ${error.response.status}`);
            debug(`响应数据: ${JSON.stringify(error.response.data)}`);
        }
        return false;
    }
};

// 刷新Nike Token
const refreshNikeToken = async () => {
    if (!AUTH.refreshToken) {
        log(`❌ 缺少refresh token，无法刷新`);
        return false;
    }

    debug(`刷新Nike Token...`);

    try {
        const response = await axios.post(`${CONFIG.accountsUrl}/token/v1`, 
            `refresh_token=${AUTH.refreshToken}&client_id=${CONFIG.clientId}&grant_type=refresh_token`,
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        const data = response.data;
        if (data && (data.accessToken || data.access_token)) {
            AUTH.accessToken = data.accessToken || data.access_token;
            AUTH.expiresIn = data.expiresIn || data.expires_in || 7200;

            debug(`Token刷新成功`);
            
            // 保存Token到缓存
            saveTokenCache();
            return true;
        } else {
            log(`❌ Token刷新失败: 响应格式错误`);
            return false;
        }
    } catch (error) {
        log(`❌ Token刷新失败: ${error.message}`);
        return false;
    }
};

// Nike每日签到
const nikeDailySignIn = async () => {
    if (!AUTH.accessToken || !AUTH.userId) {
        log(`❌ 缺少Nike认证信息，无法签到`);
        return false;
    }

    debug(`开始Nike每日签到...`);

    try {
        // 从小程序源码分析得到的签到API
        const response = await axios.get(
            `${CONFIG.nikeApiUrl}/onemp/redeem/complete_daily_sign/v2/${AUTH.userId}`,
            {
                headers: {
                    'Authorization': `Bearer ${AUTH.accessToken}`,
                    'App-Id': CONFIG.appId,
                    'nike-api-caller-id': 'nike:wechat:web:1.0',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json; charset=UTF-8'
                }
            }
        );

        const data = response.data;
        if (data) {
            const points = data.points || 0;
            log(`🎉 Nike签到成功! 获得 ${points} 积分`);
            return { success: true, points: points, data: data };
        } else {
            log(`❌ Nike签到失败: 响应为空`);
            return false;
        }
    } catch (error) {
        if (error.response && error.response.status === 401) {
            log(`⚠️ Token可能已过期，尝试刷新...`);
            const refreshSuccess = await refreshNikeToken();
            if (refreshSuccess) {
                // 递归重试签到
                return await nikeDailySignIn();
            } else {
                log(`❌ Token刷新失败，需要重新登录`);
                return false;
            }
        } else {
            log(`❌ Nike签到失败: ${error.message}`);
            if (error.response) {
                debug(`响应状态: ${error.response.status}`);
                debug(`响应数据: ${JSON.stringify(error.response.data)}`);
            }
            return false;
        }
    }
};

// 主函数
const main = async () => {
    log(`🔔 Nike签到脚本开始执行`);

    // 1. 尝试从缓存加载Token
    const cachedToken = loadTokenCache();
    if (cachedToken) {
        AUTH = cachedToken;
        log(`📦 使用缓存的Token`);
    } else {
        // 2. 尝试从nike_tokens.json加载Token
        try {
            const tokenData = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
            if (tokenData.accessToken) {
                AUTH.accessToken = tokenData.accessToken;
                AUTH.refreshToken = tokenData.refreshToken;
                AUTH.userId = tokenData.userId || extractUserIdFromToken(tokenData.accessToken);
                AUTH.expiresIn = tokenData.expiresIn || 7200;
                log(`📦 使用nike_tokens.json中的Token`);
                debug(`User ID: ${AUTH.userId}`);
            } else {
                log(`❌ nike_tokens.json中没有有效的Token`);
                return;
            }
        } catch (error) {
            log(`❌ 无法读取nike_tokens.json: ${error.message}`);
            return;
        }
    }

    // 3. 执行签到
    const signInResult = await nikeDailySignIn();

    log(`🏁 脚本执行完成`);
};

// 从命令行参数获取配置
const parseArgs = () => {
    const args = process.argv.slice(2);
    for (let i = 0; i < args.length; i++) {
        if (args[i] === '--wxcode' && i + 1 < args.length) {
            CONFIG.wxCode = args[i + 1];
        } else if (args[i] === '--wxid' && i + 1 < args.length) {
            CONFIG.wxid = args[i + 1];
        } else if (args[i] === '--debug') {
            CONFIG.debug = true;
        }
    }
};

// 执行脚本
parseArgs();
main().catch(error => {
    log(`❌ 脚本执行出错: ${error.message}`);
});
