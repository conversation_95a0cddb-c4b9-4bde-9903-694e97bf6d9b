/**
 * Nike真实Token获取脚本
 * 通过微信授权获取真实的Nike访问令牌
 * 作者：Tianxx
 * 版本：1.0
 */

const axios = require('axios');
const fs = require('fs');

// Nike配置
const NIKE_CONFIG = {
    wxid: 'wxid_ltkystdcspc822',
    appId: 'wx096c43d1829a7788',
    
    // Nike API端点
    wechatAuthUrl: 'https://wechat.nike.com.cn/wechat_auth/token/v1',
    accountsUrl: 'https://accounts.nike.com.cn',
    
    // 客户端配置
    clientId: 'b75a7f09c6f0a046a73b97532ac971b',
    redirectUri: 'https://mp-static-assets.gc.nike.com/auth/wechat-shop/index.html',
    
    // 请求头
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003D57) NetType/WIFI Language/zh_CN'
    }
};

// 日志函数
const log = (message, type = 'info') => {
    const colors = {
        info: '\x1b[37m', success: '\x1b[32m', warning: '\x1b[33m', 
        error: '\x1b[31m', debug: '\x1b[36m', reset: '\x1b[0m'
    };
    console.log(`${colors[type]}${message}${colors.reset}`);
};

// 使用wxcode获取微信授权码
async function getWxCode() {
    try {
        const wxcode = require('./wxcode');
        
        log('🔄 获取微信授权码...', 'info');
        
        const result = await wxcode.getUserInfo(
            NIKE_CONFIG.wxid,
            NIKE_CONFIG.appId,
            JSON.stringify({ action: 'get_wx_code' })
        );
        
        if (result.success && result.rawData) {
            try {
                const data = JSON.parse(result.rawData);
                if (data.code) {
                    log(`✅ 成功获取微信授权码: ${data.code.substring(0, 10)}...`, 'success');
                    return data.code;
                }
            } catch (e) {
                // 如果不是JSON，可能直接就是code
                if (typeof result.rawData === 'string' && result.rawData.length > 10) {
                    log(`✅ 成功获取微信授权码: ${result.rawData.substring(0, 10)}...`, 'success');
                    return result.rawData;
                }
            }
        }
        
        log('❌ 无法获取微信授权码', 'error');
        return null;
    } catch (error) {
        log(`❌ 获取微信授权码失败: ${error.message}`, 'error');
        return null;
    }
}

// 使用微信授权码获取Nike Token
async function getNikeTokenWithWxCode(wxCode) {
    log('🔄 使用微信授权码获取Nike Token...', 'info');
    
    const requestData = {
        appId: NIKE_CONFIG.appId,
        code: wxCode
    };
    
    log(`📡 请求数据: ${JSON.stringify(requestData)}`, 'debug');
    
    try {
        const response = await axios.post(NIKE_CONFIG.wechatAuthUrl, requestData, {
            headers: NIKE_CONFIG.headers,
            timeout: 15000,
            validateStatus: function (status) {
                return status < 500;
            }
        });
        
        log(`📊 响应状态: ${response.status}`, 'debug');
        log(`📥 响应数据: ${JSON.stringify(response.data)}`, 'debug');
        
        if (response.status === 200 && response.data) {
            const data = response.data;
            
            // 提取Token信息
            const tokenInfo = {
                accessToken: data.accessToken || data.access_token,
                refreshToken: data.refreshToken || data.refresh_token,
                userId: data.user_id || data.upmId || data.openId,
                expiresIn: data.expiresIn || data.expires_in || 7200,
                tokenType: data.tokenType || data.token_type || 'Bearer',
                scope: data.scope,
                wxid: NIKE_CONFIG.wxid,
                appId: NIKE_CONFIG.appId,
                timestamp: Date.now()
            };
            
            if (tokenInfo.accessToken) {
                log('✅ 成功获取Nike Token!', 'success');
                log(`🔑 Access Token: ${tokenInfo.accessToken.substring(0, 30)}...`, 'success');
                log(`👤 User ID: ${tokenInfo.userId}`, 'success');
                log(`⏰ 过期时间: ${tokenInfo.expiresIn}秒`, 'success');
                
                // 保存Token到文件
                saveTokenToFile(tokenInfo);
                
                return tokenInfo;
            } else {
                log('❌ 响应中没有找到accessToken', 'error');
                return null;
            }
        } else {
            log(`❌ Nike Token获取失败: ${response.status}`, 'error');
            return null;
        }
    } catch (error) {
        log(`❌ Nike Token请求失败: ${error.message}`, 'error');
        if (error.response) {
            log(`📊 错误状态: ${error.response.status}`, 'error');
            log(`📊 错误数据: ${JSON.stringify(error.response.data)}`, 'error');
        }
        return null;
    }
}

// 保存Token到文件
function saveTokenToFile(tokenInfo) {
    try {
        const tokenData = {
            ...tokenInfo,
            note: '真实的Nike认证Token',
            createdAt: new Date().toISOString()
        };
        
        fs.writeFileSync('./nike_real_tokens.json', JSON.stringify(tokenData, null, 2));
        log('💾 Token已保存到 nike_real_tokens.json', 'success');
        
        // 同时更新原来的nike_tokens.json
        fs.writeFileSync('./nike_tokens.json', JSON.stringify(tokenData, null, 2));
        log('💾 Token已更新到 nike_tokens.json', 'success');
        
    } catch (error) {
        log(`❌ 保存Token失败: ${error.message}`, 'error');
    }
}

// 验证Token有效性
async function validateToken(tokenInfo) {
    log('🔄 验证Token有效性...', 'info');
    
    // 尝试调用Nike用户信息API
    const testUrl = 'https://api.nike.com.cn/onemp/user/profile/v1';
    
    try {
        const response = await axios.get(testUrl, {
            headers: {
                'Authorization': `Bearer ${tokenInfo.accessToken}`,
                'App-Id': NIKE_CONFIG.appId,
                'Accept': 'application/json'
            },
            timeout: 10000,
            validateStatus: function (status) {
                return status < 500;
            }
        });
        
        log(`📊 验证响应状态: ${response.status}`, 'debug');
        
        if (response.status === 200) {
            log('✅ Token验证成功，可以正常使用', 'success');
            return true;
        } else if (response.status === 401) {
            log('❌ Token已过期或无效', 'error');
            return false;
        } else {
            log(`⚠️ Token验证状态未知: ${response.status}`, 'warning');
            return false;
        }
    } catch (error) {
        log(`❌ Token验证失败: ${error.message}`, 'error');
        return false;
    }
}

// 主函数
async function main() {
    log('🚀 Nike真实Token获取脚本', 'info');
    log('=====================================', 'info');
    
    // 1. 获取微信授权码
    const wxCode = await getWxCode();
    if (!wxCode) {
        log('❌ 无法获取微信授权码，请检查wxcode模块', 'error');
        return;
    }
    
    // 2. 使用授权码获取Nike Token
    const tokenInfo = await getNikeTokenWithWxCode(wxCode);
    if (!tokenInfo) {
        log('❌ 无法获取Nike Token', 'error');
        return;
    }
    
    // 3. 验证Token有效性
    const isValid = await validateToken(tokenInfo);
    if (isValid) {
        log('🎉 Nike Token获取并验证成功!', 'success');
        log('💡 现在可以使用 nike_professional_signin.js 进行签到', 'info');
    } else {
        log('⚠️ Token获取成功但验证失败，可能需要进一步调试', 'warning');
    }
    
    log('🏁 脚本执行完成', 'info');
}

// 执行脚本
if (require.main === module) {
    main().catch(error => {
        log(`❌ 脚本执行出错: ${error.message}`, 'error');
        console.error(error.stack);
    });
}

module.exports = {
    getWxCode,
    getNikeTokenWithWxCode,
    validateToken
};
