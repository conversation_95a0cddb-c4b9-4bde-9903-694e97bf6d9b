{"entryPagePath": "pages/member-store/index", "pages": ["pages/member-store/index", "pages/cart/index", "pages/profile/index", "pages/redirect/index", "pages/swoosh-center/index", "pages/category/index"], "permission": {"scope.userLocation": {"desc": "将获取你的具体位置信息，用于展示附近合适的门店及活动"}}, "networkTimeout": {"request": 15000, "downloadFile": 30000}, "plugins": {}, "tabBar": {"custom": true, "color": "#fff", "selectedColor": "#fff", "backgroundColor": "#fff", "position": "bottom", "borderStyle": "white", "list": [{"text": "商城", "pagePath": "pages/member-store/index"}, {"text": "商品分类", "pagePath": "pages/category/index"}, {"text": "福利中心", "pagePath": "pages/swoosh-center/index"}, {"text": "购物车", "pagePath": "pages/cart/index"}, {"text": "我的", "pagePath": "pages/profile/index"}]}, "preloadRule": {"pages/member-store/index": {"network": "all", "packages": ["subPackageUtils", "subPackageComponents", "product", "product-wall"]}, "pages/redirect/index": {"network": "all", "packages": []}, "pages/profile/index": {"network": "all", "packages": ["cxp", "subPackageUtils", "subPackageComponents", "settings", "order", "wallet"]}, "pages/settings/index": {"network": "all", "packages": ["address", "agreements"]}, "pages/cart/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/red-packet/preview": {"network": "all", "packages": ["subPackageComponents"]}, "pages/settings/helpcenter/faq-item": {"network": "all", "packages": ["cxp"]}, "pages/redeem-center/index": {"network": "all", "packages": ["subPackageUtils", "subPackageComponents"]}, "pages/swoosh-center/index": {"network": "all", "packages": ["cxp", "subPackageComponents"]}, "pages/cxp/web/photo": {"network": "all", "packages": ["h5-navigate-to-weapp"]}, "pages/cxp/check-in/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/cxp/experience/joined/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/cxp/experience/detail/single/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/cxp/experience/detail/nby/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/cxp/experience/detail/expert/index": {"network": "all", "packages": ["subPackageComponents"]}, "pages/cxp/index/index": {"network": "all", "packages": ["subPackageComponents"]}}, "renderer": "webview", "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>", "getLocation"], "__usePrivacyCheck__": true, "componentFramework": "exparser", "window": {"navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationStyle": "custom"}, "subPackages": [{"root": "pages/index/", "name": "index", "pages": ["index", "web"]}, {"root": "pages/wallet/", "name": "wallet", "pages": ["index"]}, {"root": "pages/auth/", "name": "auth", "pages": ["member-benefits", "unite", "accounts", "accounts-deeplink"]}, {"root": "pages/events/", "name": "events", "pages": ["event", "campaign", "my-events"]}, {"root": "pages/unlocks/", "name": "unlocks", "pages": ["thread_active", "thread_redeemed", "thread_404"]}, {"root": "pages/address/", "name": "address", "pages": ["index", "add"]}, {"root": "pages/thread/", "name": "thread", "pages": ["feed"]}, {"root": "pages/order/", "name": "order", "pages": ["detail", "detail-guest", "transfer", "index", "guest", "guest-2", "guest-3"]}, {"root": "pages/product/", "name": "product", "pages": ["index", "detail", "pay-order", "pdp-general", "ask-friends/index", "ask-friends/help-me-pick"]}, {"root": "pages/settings/", "name": "settings", "pages": ["helpcenter/index", "helpcenter/faq", "helpcenter/faq-item", "index", "delete-account", "modify-phone", "validate-code"]}, {"root": "pages/agreements/", "name": "agreements", "pages": ["index"]}, {"root": "pages/product-wall/", "name": "product-wall", "pages": ["index", "sub-category/index", "filter", "search"]}, {"root": "pages/post-purchase/", "name": "post-purchase", "pages": ["index"]}, {"root": "pages/red-packet/", "name": "red-packet", "pages": ["index", "preview"]}, {"root": "pages/liveplay/", "name": "liveplay", "plugins": {}, "pages": ["livePlayer", "plugin_/wx2b03c6e691cd7370/pages/live-player-plugin", "plugin_/wx2b03c6e691cd7370/wxlive-components/ext-player/address-preview/address-preview", "plugin_/wx2b03c6e691cd7370/wxlive-components/ext-player/complaint-comment/complaint-comment", "plugin_/wx2b03c6e691cd7370/wxlive-components/ext-player/complaint-room/complaint-room"]}, {"root": "pages/stores/", "name": "stores", "pages": ["index", "detail", "map"]}, {"root": "subPackageComponents/", "name": "subPackageComponents", "pages": ["nike-pass/index"]}, {"root": "subPackageUtils/", "name": "subPackageUtils", "pages": ["index/index"]}, {"root": "pages/redeem-center/", "name": "redeem-center", "pages": ["index", "usage-detail", "prize-detail", "my-prize"]}, {"root": "pages/cxp/", "name": "cxp", "pages": ["index/index", "cities/index", "cities/search/index", "experience/detail/single/index", "experience/detail/nby/index", "experience/detail/expert/index", "experience/registration/index", "experience/joined/index", "web/photo", "contact/customer", "my-experience/index", "binding/index", "check-in/index"]}, {"root": "pages/h5-navigate-to-weapp/", "name": "h5-navigate-to-weapp", "pages": ["index"]}]}