/**
 * Nike云函数data字段抓取脚本
 * 专门抓取CloudCallFunction的data参数
 */

console.log("🎯 Nike Data抓取脚本 - 等待签到操作...");

var capturedData = [];

// Hook WebView中的JavaScript执行
if (ObjC.available) {
    var WKWebView = ObjC.classes.WKWebView;
    if (WKWebView) {
        var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
        if (evaluateJS) {
            Interceptor.attach(evaluateJS.implementation, {
                onEnter: function(args) {
                    try {
                        var jsCode = new ObjC.Object(args[2]).toString();
                        
                        // 抓取包含签到相关的JS代码
                        if (jsCode.includes('complete_daily_sign') || 
                            jsCode.includes('callContainer') ||
                            jsCode.includes('CloudCallFunction') ||
                            jsCode.includes('redeem')) {
                            
                            console.log("\n🎉 发现签到相关调用!");
                            console.log("=" * 60);
                            console.log("时间:", new Date().toISOString());
                            console.log("JS代码:", jsCode);
                            console.log("=" * 60);
                            
                            // 保存数据
                            capturedData.push({
                                timestamp: new Date().toISOString(),
                                type: 'javascript',
                                content: jsCode
                            });
                        }
                        
                        // 抓取包含data字段的调用
                        if (jsCode.includes('"data"') && 
                            (jsCode.includes('api_name') || jsCode.includes('complete_daily_sign'))) {
                            
                            console.log("\n📦 发现data字段!");
                            console.log("=" * 60);
                            console.log("Data JS:", jsCode);
                            console.log("=" * 60);
                            
                            // 尝试提取data字段
                            var dataMatch = jsCode.match(/"data"\s*:\s*"([^"]+)"/);
                            if (dataMatch) {
                                console.log("🎯 提取的data:", dataMatch[1]);
                            }
                        }
                        
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    }
    
    // Hook NSURLSession网络请求
    var NSURLSession = ObjC.classes.NSURLSession;
    if (NSURLSession) {
        var dataTask = NSURLSession['- dataTaskWithRequest:completionHandler:'];
        if (dataTask) {
            Interceptor.attach(dataTask.implementation, {
                onEnter: function(args) {
                    try {
                        var request = new ObjC.Object(args[2]);
                        var url = request.URL().absoluteString().toString();
                        
                        // 抓取CloudCallFunction请求
                        if (url.includes('CloudCallFunction') || 
                            url.includes('container_service') ||
                            url.includes('servicewechat.com')) {
                            
                            console.log("\n🌐 发现云函数网络请求!");
                            console.log("URL:", url);
                            
                            // 获取请求体
                            var httpBody = request.HTTPBody();
                            if (httpBody) {
                                try {
                                    var bodyData = new ObjC.Object(httpBody);
                                    var bodyString = bodyData.toString();
                                    
                                    console.log("📦 请求体:", bodyString);
                                    
                                    // 尝试解析JSON
                                    try {
                                        var jsonData = JSON.parse(bodyString);
                                        if (jsonData.data) {
                                            console.log("🎯 发现data字段:", jsonData.data);
                                            
                                            // 保存完整的请求数据
                                            capturedData.push({
                                                timestamp: new Date().toISOString(),
                                                type: 'http_request',
                                                url: url,
                                                data: jsonData
                                            });
                                        }
                                    } catch (e) {
                                        console.log("📦 原始请求体:", bodyString);
                                    }
                                } catch (e) {
                                    console.log("❌ 无法读取请求体");
                                }
                            }
                            
                            // 获取请求头
                            var headers = {};
                            try {
                                var allHeaders = request.allHTTPHeaderFields();
                                if (allHeaders) {
                                    var keys = allHeaders.allKeys();
                                    for (var i = 0; i < keys.count(); i++) {
                                        var key = keys.objectAtIndex_(i).toString();
                                        var value = allHeaders.objectForKey_(key).toString();
                                        headers[key] = value;
                                    }
                                    console.log("📋 请求头:", JSON.stringify(headers, null, 2));
                                }
                            } catch (e) {
                                console.log("❌ 无法读取请求头");
                            }
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    }
    
    console.log("✅ Hook设置完成，请在小程序中点击签到");
}

// 全局函数 - 显示捕获的数据
this.showCapturedData = function() {
    console.log("\n📊 捕获数据统计");
    console.log("=====================================");
    console.log("总共捕获:", capturedData.length, "条数据");
    
    capturedData.forEach((item, index) => {
        console.log(`\n[${index + 1}] ${item.type} - ${item.timestamp}`);
        if (item.type === 'javascript') {
            console.log("JS代码:", item.content.substring(0, 200) + "...");
        } else if (item.type === 'http_request') {
            console.log("URL:", item.url);
            console.log("Data:", JSON.stringify(item.data, null, 2));
        }
    });
    
    return capturedData;
};

// 全局函数 - 导出签到data
this.exportSignInData = function() {
    var signInData = capturedData.filter(item => 
        (item.content && item.content.includes('complete_daily_sign')) ||
        (item.data && JSON.stringify(item.data).includes('complete_daily_sign'))
    );
    
    if (signInData.length === 0) {
        console.log("❌ 暂无签到相关数据");
        return null;
    }
    
    console.log("\n🎯 签到相关数据:");
    signInData.forEach((item, index) => {
        console.log(`\n[签到数据 ${index + 1}]`);
        if (item.data) {
            console.log("完整请求数据:");
            console.log(JSON.stringify(item.data, null, 2));
            
            // 生成curl命令
            console.log("\n📋 对应的curl命令:");
            console.log(`curl -X POST "http://820121.xyz:8800/api/Wxapp/CloudCallFunction" \\`);
            console.log(`  -H "accept: application/json" \\`);
            console.log(`  -H "content-type: application/json" \\`);
            console.log(`  -d '${JSON.stringify(item.data)}'`);
        }
    });
    
    return signInData;
};

// 全局函数 - 生成签到脚本
this.generateSignInScript = function() {
    var signInData = this.exportSignInData();
    if (!signInData || signInData.length === 0) {
        return null;
    }
    
    var latestData = signInData[signInData.length - 1];
    if (!latestData.data) {
        return null;
    }
    
    var scriptCode = `
// Nike自动签到脚本 - 基于Frida抓取数据生成
const axios = require('axios');

async function nikeAutoSignIn() {
    const requestData = ${JSON.stringify(latestData.data, null, 4)};
    
    try {
        const response = await axios.post(
            'http://820121.xyz:8800/api/Wxapp/CloudCallFunction',
            requestData,
            {
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                }
            }
        );
        
        console.log('✅ 签到成功:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ 签到失败:', error.message);
        return null;
    }
}

// 执行签到
nikeAutoSignIn();
`;
    
    console.log("\n🚀 生成的签到脚本:");
    console.log(scriptCode);
    
    return scriptCode;
};

console.log("🎯 Data抓取脚本已就绪，等待Nike小程序签到操作...");
console.log("💡 操作完成后，输入以下命令查看结果:");
console.log("  showCapturedData() - 查看所有捕获数据");
console.log("  exportSignInData() - 导出签到数据");
console.log("  generateSignInScript() - 生成签到脚本");
