# Nike小程序Frida调试指南

## 🎯 目标
通过Frida Hook技术捕获Nike小程序的登录和签到流程，获取真实的API调用参数。

## 📋 准备工作

### 1. 安装Frida
```bash
# 安装Frida工具
pip install frida-tools

# 验证安装
frida --version
```

### 2. 准备Android设备
- 已Root的Android手机
- 开启USB调试
- 安装frida-server

### 3. 下载frida-server
```bash
# 查看设备架构
adb shell getprop ro.product.cpu.abi

# 下载对应版本的frida-server
# 从 https://github.com/frida/frida/releases 下载
# 例如: frida-server-16.1.4-android-arm64.xz
```

### 4. 安装frida-server到手机
```bash
# 解压并推送到手机
adb push frida-server /data/local/tmp/
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server (需要root权限)
adb shell "su -c '/data/local/tmp/frida-server &'"
```

## 🚀 使用方法

### 方法1: 使用启动脚本 (推荐)

**Windows:**
```cmd
# 双击运行或在命令行执行
start_nike_debug.bat
```

**Linux/Mac:**
```bash
# 给脚本执行权限
chmod +x start_nike_debug.sh

# 运行脚本
./start_nike_debug.sh
```

### 方法2: 手动启动

```bash
# 检查设备连接
frida-ls-devices

# 检查微信进程
frida-ps -U | grep wechat

# 启动Hook
frida -U com.tencent.mm -l nike_frida_debug.js
```

## 📱 操作步骤

1. **启动Hook脚本**
   - 运行启动脚本
   - 确认看到 "Hook初始化完成" 消息

2. **在手机上操作**
   - 打开微信
   - 进入Nike小程序
   - 执行登录操作
   - 执行签到操作

3. **查看捕获数据**
   在Frida终端中输入以下命令：
   ```javascript
   // 查看数据摘要
   summary()
   
   // 获取最新微信Code
   getLatestWxCode()
   
   // 获取最新Nike Token
   getLatestNikeToken()
   
   // 导出所有数据
   exportData()
   ```

## 🔍 预期输出

### 成功Hook后会看到：
```
🚀 Nike小程序Frida调试脚本已启动
=====================================
📱 目标应用: 微信 (com.tencent.mm)
🎯 目标小程序: Nike小程序 (wx096c43d1829a7788)
=====================================

[+] 开始初始化Hook...
[✓] 找到微信登录类
[✓] 找到云函数容器类
[✓] 找到OkHttp客户端
[✓] 找到小程序网络管理器

[✅] Hook初始化完成!
[💡] 请在手机上打开Nike小程序并执行签到操作
[📊] 输入 'summary()' 查看捕获数据摘要
[💾] 输入 'export()' 导出所有数据
=====================================
```

### 捕获到数据时会显示：
```
[🔑] 检测到微信登录调用
[🎉] 成功捕获微信Code: 0a3bug100QihIU11ze100brYsY3bug1G

[🌐] HTTP请求: POST https://wechat.nike.com.cn/wechat_auth/token/v1
[🔐] Nike登录请求
Headers: {
  "Authorization": "Bearer ...",
  "App-Id": "wechat:mp:wx096c43d1829a7788"
}

[🔐] Nike登录响应: {"accessToken":"eyJhbGc...","expiresIn":"3600"}

[☁️] 检测到Nike云函数调用
参数: {"path":"/onemp/redeem/complete_daily_sign/v2/ogvnS5FxB9Zfq54dnkV434lnpggI",...}

[🎯] 签到请求!
[🎉] 签到响应: {"points":3,"message":"签到成功"}
```

## 📊 数据分析

### 捕获的关键数据：
1. **微信Code** - 用于Nike登录
2. **Nike AccessToken** - 用于API认证
3. **签到请求参数** - 完整的签到API调用
4. **云函数调用** - 微信云开发的调用方式

### 使用捕获的数据：
```javascript
// 在Frida终端中查看数据
var data = exportData();

// 获取最新的微信Code
var wxCode = data.wxCodes[data.wxCodes.length - 1].code;

// 获取Nike Token响应
var tokenResponse = JSON.parse(data.nikeTokens[data.nikeTokens.length - 1].response);
var accessToken = tokenResponse.accessToken;

// 获取签到请求
var signInRequest = data.signInRequests[data.signInRequests.length - 1];
```

## 🛠️ 故障排除

### 常见问题：

1. **"未找到微信进程"**
   - 确保微信正在运行
   - 检查frida-server是否启动
   - 验证USB调试连接

2. **"Hook初始化失败"**
   - 检查手机是否已Root
   - 确认frida-server版本匹配
   - 重启frida-server

3. **"没有捕获到数据"**
   - 确保在Hook启动后再操作小程序
   - 检查小程序版本是否匹配
   - 尝试重新登录小程序

### 调试命令：
```bash
# 检查frida-server状态
adb shell ps | grep frida

# 重启frida-server
adb shell "su -c 'killall frida-server'"
adb shell "su -c '/data/local/tmp/frida-server &'"

# 查看详细日志
frida -U com.tencent.mm -l nike_frida_debug.js --debug
```

## 📝 下一步

获取到完整的签到数据后，可以：

1. **分析API调用模式**
2. **提取关键参数**
3. **构建自动化脚本**
4. **实现批量签到**

## ⚠️ 注意事项

- 仅用于学习和研究目的
- 遵守相关服务条款
- 不要过度频繁调用API
- 保护个人隐私数据
