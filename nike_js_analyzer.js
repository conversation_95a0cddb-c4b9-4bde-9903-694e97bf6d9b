/**
 * Nike小程序JavaScript分析脚本
 * 专门分析JavaScript执行中的API调用和数据传输
 */

console.log("🎯 Nike JavaScript分析脚本");
console.log("=====================================");
console.log("📱 专注分析: JavaScript中的API调用");
console.log("🔍 寻找: 签到、积分、任务相关的调用");
console.log("=====================================\n");

var capturedAPIs = [];
var jsStats = { total: 0, nike: 0, api: 0, important: 0 };

function log(message, color = 'white') {
    const colors = {
        red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m',
        blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m',
        white: '\x1b[37m', reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

// 分析JavaScript代码，提取API调用信息
function analyzeJavaScript(jsCode) {
    const analysis = {
        isImportant: false,
        apiCalls: [],
        dataObjects: [],
        urls: [],
        tokens: [],
        userIds: [],
        keywords: []
    };
    
    // 检查重要关键词
    const importantKeywords = [
        'complete_daily_sign', 'redeem', 'points', 'signin', 'checkin',
        'task', 'mission', 'reward', 'prize', 'coupon', 'exchange',
        'user_task', 'recent_actions', 'redeem_center_info'
    ];
    
    const foundKeywords = importantKeywords.filter(keyword => 
        jsCode.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (foundKeywords.length > 0) {
        analysis.isImportant = true;
        analysis.keywords = foundKeywords;
    }
    
    // 提取API调用
    const apiPatterns = [
        /\.get\s*\(\s*["']([^"']+)["']/g,
        /\.post\s*\(\s*["']([^"']+)["']/g,
        /\.request\s*\(\s*["']([^"']+)["']/g,
        /fetch\s*\(\s*["']([^"']+)["']/g,
        /wx\.request\s*\(\s*{[^}]*url\s*:\s*["']([^"']+)["']/g
    ];
    
    apiPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(jsCode)) !== null) {
            analysis.apiCalls.push(match[1]);
        }
    });
    
    // 提取URL
    const urlPattern = /https?:\/\/[^\s"']+/g;
    let urlMatch;
    while ((urlMatch = urlPattern.exec(jsCode)) !== null) {
        analysis.urls.push(urlMatch[0]);
    }
    
    // 提取Token
    const tokenPattern = /Bearer\s+([a-zA-Z0-9\-_.]+)/g;
    let tokenMatch;
    while ((tokenMatch = tokenPattern.exec(jsCode)) !== null) {
        analysis.tokens.push(tokenMatch[1].substring(0, 20) + '...');
    }
    
    // 提取用户ID (UUID格式)
    const userIdPattern = /[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g;
    let userIdMatch;
    while ((userIdMatch = userIdPattern.exec(jsCode)) !== null) {
        analysis.userIds.push(userIdMatch[0]);
    }
    
    // 提取数据对象
    const dataPatterns = [
        /"data"\s*:\s*({[^}]+})/g,
        /"params"\s*:\s*({[^}]+})/g,
        /"body"\s*:\s*({[^}]+})/g
    ];
    
    dataPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(jsCode)) !== null) {
            try {
                analysis.dataObjects.push(JSON.parse(match[1]));
            } catch (e) {
                analysis.dataObjects.push(match[1]);
            }
        }
    });
    
    return analysis;
}

if (ObjC.available) {
    var WKWebView = ObjC.classes.WKWebView;
    if (WKWebView) {
        var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
        if (evaluateJS) {
            Interceptor.attach(evaluateJS.implementation, {
                onEnter: function(args) {
                    try {
                        var jsCode = new ObjC.Object(args[2]).toString();
                        jsStats.total++;
                        
                        // 基本过滤 - 只关注可能相关的代码
                        if (jsCode.includes('nike') || 
                            jsCode.includes('redeem') || 
                            jsCode.includes('api') ||
                            jsCode.includes('request') ||
                            jsCode.includes('complete_daily_sign') ||
                            jsCode.includes('points') ||
                            jsCode.includes('task')) {
                            
                            jsStats.nike++;
                            
                            // 深度分析
                            const analysis = analyzeJavaScript(jsCode);
                            
                            if (analysis.isImportant) {
                                jsStats.important++;
                                
                                log(`\n🎯 [重要JS #${jsStats.important}] ${new Date().toLocaleTimeString()}`, 'green');
                                log(`🔑 关键词: ${analysis.keywords.join(', ')}`, 'yellow');
                                
                                if (analysis.apiCalls.length > 0) {
                                    log(`📡 API调用: ${analysis.apiCalls.join(', ')}`, 'cyan');
                                }
                                
                                if (analysis.urls.length > 0) {
                                    log(`🔗 URL: ${analysis.urls.join(', ')}`, 'blue');
                                }
                                
                                if (analysis.tokens.length > 0) {
                                    log(`🔐 Token: ${analysis.tokens.join(', ')}`, 'magenta');
                                }
                                
                                if (analysis.userIds.length > 0) {
                                    log(`👤 用户ID: ${analysis.userIds.join(', ')}`, 'cyan');
                                }
                                
                                if (analysis.dataObjects.length > 0) {
                                    log(`📦 数据对象: ${analysis.dataObjects.length}个`, 'yellow');
                                    analysis.dataObjects.forEach((obj, i) => {
                                        log(`  [${i+1}] ${JSON.stringify(obj).substring(0, 100)}...`, 'white');
                                    });
                                }
                                
                                log(`📝 代码: ${jsCode.substring(0, 200)}...`, 'white');
                                
                                // 保存重要数据
                                capturedAPIs.push({
                                    id: jsStats.important,
                                    timestamp: new Date().toISOString(),
                                    analysis: analysis,
                                    fullCode: jsCode
                                });
                                
                                // 特别标记签到
                                if (jsCode.includes('complete_daily_sign')) {
                                    log("🎉 这可能是签到相关的调用！", 'green');
                                }
                            } else if (analysis.apiCalls.length > 0 || analysis.urls.length > 0) {
                                jsStats.api++;
                                log(`\n📱 [API调用 #${jsStats.api}] ${new Date().toLocaleTimeString()}`, 'blue');
                                
                                if (analysis.apiCalls.length > 0) {
                                    log(`📡 API: ${analysis.apiCalls.join(', ')}`, 'cyan');
                                }
                                if (analysis.urls.length > 0) {
                                    log(`🔗 URL: ${analysis.urls[0]}`, 'blue');
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    }
    
    log("✅ JavaScript分析器已启动", 'green');
    log("💡 现在请在Nike小程序中进行操作:", 'yellow');
    log("  - 查看积分记录", 'white');
    log("  - 浏览任务列表", 'white');
    log("  - 点击任何按钮", 'white');
    log("=====================================\n", 'white');
}

// 实时统计
setInterval(function() {
    if (jsStats.total > 0) {
        log(`📊 统计 - 总计:${jsStats.total} | Nike相关:${jsStats.nike} | API调用:${jsStats.api} | 重要:${jsStats.important}`, 'blue');
    }
}, 20000);

// 全局函数
this.showStats = function() {
    log("\n📊 JavaScript分析统计", 'cyan');
    log("=====================================", 'white');
    log(`总JavaScript执行: ${jsStats.total}`, 'white');
    log(`Nike相关: ${jsStats.nike}`, 'yellow');
    log(`API调用: ${jsStats.api}`, 'blue');
    log(`重要调用: ${jsStats.important}`, 'green');
    log("=====================================\n", 'white');
    return jsStats;
};

this.showImportantCalls = function() {
    if (capturedAPIs.length === 0) {
        log("❌ 暂无重要调用", 'red');
        return null;
    }
    
    log(`\n🎯 重要调用列表 (${capturedAPIs.length}个):`, 'green');
    capturedAPIs.forEach(item => {
        log(`\n[${item.id}] ${item.timestamp}`, 'yellow');
        log(`关键词: ${item.analysis.keywords.join(', ')}`, 'cyan');
        if (item.analysis.apiCalls.length > 0) {
            log(`API: ${item.analysis.apiCalls.join(', ')}`, 'blue');
        }
        if (item.analysis.urls.length > 0) {
            log(`URL: ${item.analysis.urls[0]}`, 'magenta');
        }
        log(`代码: ${item.fullCode.substring(0, 150)}...`, 'white');
    });
    
    return capturedAPIs;
};

this.findSignIn = function() {
    const signInCalls = capturedAPIs.filter(item => 
        item.analysis.keywords.some(keyword => 
            keyword.includes('sign') || keyword.includes('checkin') || keyword.includes('daily')
        )
    );
    
    if (signInCalls.length === 0) {
        log("❌ 暂无签到相关调用", 'red');
        return null;
    }
    
    log(`\n🎉 签到相关调用 (${signInCalls.length}个):`, 'green');
    signInCalls.forEach(item => {
        log(`\n[签到 ${item.id}] ${item.timestamp}`, 'yellow');
        log(`完整代码:`, 'cyan');
        console.log(item.fullCode);
    });
    
    return signInCalls;
};

this.searchKeyword = function(keyword) {
    const results = capturedAPIs.filter(item => 
        item.fullCode.toLowerCase().includes(keyword.toLowerCase())
    );
    
    log(`\n🔍 搜索 "${keyword}" 结果 (${results.length}个):`, 'cyan');
    results.forEach(item => {
        log(`[${item.id}] ${item.timestamp} - ${item.analysis.keywords.join(', ')}`, 'yellow');
    });
    
    return results;
};

log("🎯 JavaScript分析器已就绪", 'green');
log("💡 可用命令:", 'yellow');
log("  showStats() - 显示统计", 'white');
log("  showImportantCalls() - 显示重要调用", 'white');
log("  findSignIn() - 查找签到相关", 'white');
log("  searchKeyword('关键词') - 搜索关键词", 'white');
