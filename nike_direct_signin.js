/**
 * Nike小程序直接签到脚本
 * 基于源码分析直接构造云函数调用
 * 作者：Tianxx
 * 版本：3.0
 */

const fs = require('fs');

// 从源码分析得到的关键信息
const NIKE_CONFIG = {
    // 云开发配置 (从config.js获取)
    wxCloud: {
        envId: "test-52toys-7gc5xgy7a72c959f",
        excludeCredentials: "cloudbase-access-token", 
        gatewayId: "test-8g5tq0ha6215e83c"
    },
    
    // API配置
    apiPath: "/onemp/redeem/complete_daily_sign/v2",
    host: "api.nike.com.cn",
    appId: "wechat:mp:wx096c43d1829a7788",
    
    // 请求头配置
    commonHeaders: {
        "Accept": "application/json",
        "Content-Type": "application/json; charset=UTF-8",
        "nike-api-caller-id": "nike:wechat:web:1.0",
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003D57) NetType/WIFI Language/zh_CN"
    }
};

// 从nike_tokens.json读取认证信息
function loadNikeTokens() {
    try {
        const tokenData = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
        console.log('✅ 成功加载Nike认证信息');
        console.log(`📋 用户ID: ${tokenData.userId}`);
        console.log(`🔑 Token: ${tokenData.accessToken.substring(0, 20)}...`);
        return tokenData;
    } catch (error) {
        console.log('❌ 无法读取nike_tokens.json:', error.message);
        return null;
    }
}

// 构造云函数调用参数 (基于HttpClient.js源码)
function buildCloudContainerParams(userId, accessToken) {
    const path = `${NIKE_CONFIG.apiPath}/${userId}`;
    
    const containerParams = {
        path: path,
        method: "GET",
        header: {
            "X-WX-EXCLUDE-CREDENTIALS": NIKE_CONFIG.wxCloud.excludeCredentials,
            "X-WX-GATEWAY-ID": NIKE_CONFIG.wxCloud.gatewayId,
            "HOST": NIKE_CONFIG.host,
            "Authorization": `Bearer ${accessToken}`,
            "App-Id": NIKE_CONFIG.appId,
            ...NIKE_CONFIG.commonHeaders
        }
    };
    
    return containerParams;
}

// 模拟云函数调用 (使用wxcode.js的getUserInfo方法)
async function callNikeSignInViaCloud(userId, accessToken) {
    const wxcode = require('./wxcode');
    
    console.log('🔄 构造云函数调用参数...');
    const containerParams = buildCloudContainerParams(userId, accessToken);
    
    console.log('📋 云函数调用参数:');
    console.log(JSON.stringify(containerParams, null, 2));
    
    // 使用wxcode的云函数调用接口
    const cloudFunctionData = {
        action: 'callContainer',
        envId: NIKE_CONFIG.wxCloud.envId,
        params: containerParams
    };
    
    console.log('\n🚀 发起云函数调用...');
    
    try {
        // 使用wxcode的getUserInfo方法来调用云函数
        const result = await wxcode.getUserInfo(
            'wxid_ltkystdcspc822', // 您的微信ID
            'wx096c43d1829a7788',  // Nike小程序ID
            JSON.stringify(cloudFunctionData)
        );
        
        if (result.success) {
            console.log('✅ 云函数调用成功');
            console.log('📥 返回数据:', result.rawData);
            
            // 尝试解析签到结果
            try {
                const signInResult = JSON.parse(result.rawData);
                if (signInResult.points !== undefined) {
                    console.log(`🎉 签到成功! 获得 ${signInResult.points} 积分`);
                    return { success: true, points: signInResult.points, data: signInResult };
                } else {
                    console.log('📊 签到响应:', signInResult);
                    return { success: true, data: signInResult };
                }
            } catch (e) {
                console.log('📊 原始响应:', result.rawData);
                return { success: true, data: result.rawData };
            }
        } else {
            console.log('❌ 云函数调用失败:', result.error);
            return { success: false, error: result.error };
        }
    } catch (error) {
        console.log('❌ 云函数调用异常:', error.message);
        return { success: false, error: error.message };
    }
}

// 直接HTTP请求方式 (备用方案)
async function directHttpSignIn(userId, accessToken) {
    const axios = require('axios');
    
    console.log('\n🔄 尝试直接HTTP请求...');
    
    const url = `https://${NIKE_CONFIG.host}${NIKE_CONFIG.apiPath}/${userId}`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
        'App-Id': NIKE_CONFIG.appId,
        'HOST': NIKE_CONFIG.host,
        ...NIKE_CONFIG.commonHeaders
    };
    
    console.log('📋 请求信息:');
    console.log(`URL: ${url}`);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    
    try {
        const response = await axios.get(url, { headers });
        console.log('✅ HTTP请求成功');
        console.log('📥 响应数据:', response.data);
        return { success: true, data: response.data };
    } catch (error) {
        console.log('❌ HTTP请求失败:', error.message);
        if (error.response) {
            console.log('📊 错误响应:', error.response.data);
            console.log('📊 状态码:', error.response.status);
        }
        return { success: false, error: error.message };
    }
}

// 生成可用的API调用信息
function generateAPIInfo(userId, accessToken) {
    const containerParams = buildCloudContainerParams(userId, accessToken);
    
    console.log('\n📋 完整的API调用信息:');
    console.log('=====================================');
    console.log('🎯 签到接口信息:');
    console.log(`URL: https://${NIKE_CONFIG.host}${NIKE_CONFIG.apiPath}/${userId}`);
    console.log(`方法: GET`);
    console.log(`认证: Bearer ${accessToken.substring(0, 20)}...`);
    console.log('\n🔧 云函数参数:');
    console.log(JSON.stringify(containerParams, null, 2));
    
    console.log('\n📋 curl命令:');
    console.log(`curl -X GET \\
  "https://${NIKE_CONFIG.host}${NIKE_CONFIG.apiPath}/${userId}" \\
  -H "Authorization: Bearer ${accessToken}" \\
  -H "App-Id: ${NIKE_CONFIG.appId}" \\
  -H "HOST: ${NIKE_CONFIG.host}" \\
  -H "nike-api-caller-id: nike:wechat:web:1.0" \\
  -H "Accept: application/json"`);
    
    console.log('\n🌐 微信云开发调用:');
    console.log(`wx.cloud.callContainer(${JSON.stringify(containerParams, null, 2)})`);
    
    return containerParams;
}

// 主函数
async function main() {
    console.log('🚀 Nike直接签到脚本 v3.0');
    console.log('=====================================\n');
    
    // 1. 加载认证信息
    const tokens = loadNikeTokens();
    if (!tokens) {
        console.log('❌ 无法获取认证信息，请先运行登录脚本');
        return;
    }
    
    const { userId, accessToken } = tokens;
    
    // 2. 生成API调用信息
    console.log('\n📋 生成API调用信息...');
    generateAPIInfo(userId, accessToken);
    
    // 3. 尝试云函数调用
    console.log('\n🎯 方案1: 云函数调用');
    const cloudResult = await callNikeSignInViaCloud(userId, accessToken);
    
    if (!cloudResult.success) {
        // 4. 备用方案：直接HTTP请求
        console.log('\n🎯 方案2: 直接HTTP请求');
        const httpResult = await directHttpSignIn(userId, accessToken);
    }
    
    console.log('\n🏁 脚本执行完成');
}

// 执行脚本
if (require.main === module) {
    main().catch(error => {
        console.log('❌ 脚本执行出错:', error.message);
    });
}

module.exports = {
    buildCloudContainerParams,
    generateAPIInfo,
    callNikeSignInViaCloud,
    directHttpSignIn
};
