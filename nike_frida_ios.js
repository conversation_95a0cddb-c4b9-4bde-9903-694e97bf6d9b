/**
 * Nike小程序Frida调试脚本 - iOS版本
 * 适用于iPhone微信小程序Hook
 * 使用方法: frida -U WeChat -l nike_frida_ios.js
 */

console.log("\n🍎 Nike小程序iOS Frida调试脚本已启动");
console.log("=====================================");
console.log("📱 目标应用: 微信 (WeChat)");
console.log("🎯 目标小程序: Nike小程序 (wx096c43d1829a7788)");
console.log("=====================================\n");

// 全局数据存储
var hookData = {
    wxCodes: [],
    nikeTokens: [],
    signInRequests: [],
    networkRequests: [],
    jsApiCalls: []
};

// 颜色输出函数
function colorLog(message, color = 'white') {
    const colors = {
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m',
        white: '\x1b[37m',
        reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

// ==================== 1. Hook NSURLSession网络请求 ====================
function hookNSURLSession() {
    try {
        var NSURLSession = ObjC.classes.NSURLSession;
        var NSURLRequest = ObjC.classes.NSURLRequest;
        
        if (NSURLSession && NSURLRequest) {
            colorLog("[✓] 找到NSURLSession类", 'green');
            
            // Hook dataTaskWithRequest
            var originalDataTask = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            
            Interceptor.attach(originalDataTask.implementation, {
                onEnter: function(args) {
                    var request = new ObjC.Object(args[2]);
                    var url = request.URL().absoluteString().toString();
                    var method = request.HTTPMethod().toString();
                    
                    // 只关注Nike相关请求
                    if (url.includes('nike.com') || url.includes('wechat')) {
                        colorLog("\n[🌐] iOS网络请求: " + method + " " + url, 'cyan');
                        
                        // 获取请求头
                        var headers = {};
                        try {
                            var allHeaders = request.allHTTPHeaderFields();
                            if (allHeaders) {
                                var keys = allHeaders.allKeys();
                                for (var i = 0; i < keys.count(); i++) {
                                    var key = keys.objectAtIndex_(i).toString();
                                    var value = allHeaders.objectForKey_(key).toString();
                                    headers[key] = value;
                                }
                            }
                        } catch (e) {
                            colorLog("[-] 获取请求头失败: " + e, 'red');
                        }
                        
                        // 获取请求体
                        var body = null;
                        try {
                            var httpBody = request.HTTPBody();
                            if (httpBody) {
                                body = httpBody.toString();
                            }
                        } catch (e) {
                            // 忽略
                        }
                        
                        // 保存重要请求
                        if (url.includes('wechat_auth/token') || 
                            url.includes('complete_daily_sign') ||
                            url.includes('redeem')) {
                            
                            var requestData = {
                                url: url,
                                method: method,
                                headers: headers,
                                body: body,
                                timestamp: new Date().toISOString()
                            };
                            
                            if (url.includes('wechat_auth/token')) {
                                colorLog("[🔐] Nike登录请求", 'yellow');
                                hookData.networkRequests.push(requestData);
                            } else if (url.includes('complete_daily_sign')) {
                                colorLog("[🎯] 签到请求!", 'green');
                                hookData.signInRequests.push(requestData);
                            }
                            
                            colorLog("Headers: " + JSON.stringify(headers, null, 2), 'blue');
                            if (body) {
                                colorLog("Body: " + body, 'yellow');
                            }
                        }
                    }
                },
                onLeave: function(retval) {
                    // 可以在这里处理返回值
                }
            });
        }
    } catch (e) {
        colorLog("[-] Hook NSURLSession失败: " + e, 'red');
    }
}

// ==================== 2. Hook微信小程序专用网络层 ====================
function hookWeChatMiniProgramNetwork() {
    try {
        colorLog("[🔍] 开始Hook微信小程序网络层...", 'yellow');

        // 1. Hook WAJSEventHandler相关的网络请求
        var classes = Object.keys(ObjC.classes);
        var waClasses = classes.filter(name =>
            name.includes('WAJSEventHandler') ||
            name.includes('WAWebViewPlugin') ||
            name.includes('MBEventHandler')
        );

        colorLog("[📋] 找到小程序事件处理类: " + waClasses.length + "个", 'blue');

        waClasses.forEach(className => {
            try {
                var cls = ObjC.classes[className];
                if (cls) {
                    var methods = cls.$ownMethods;
                    methods.forEach(method => {
                        if (method.includes('request') ||
                            method.includes('network') ||
                            method.includes('http') ||
                            method.includes('api')) {
                            try {
                                colorLog("[🎯] Hook小程序网络方法: " + className + "." + method, 'cyan');
                                Interceptor.attach(cls[method].implementation, {
                                    onEnter: function(args) {
                                        colorLog("[📡] 小程序网络调用: " + className + "." + method, 'green');

                                        // 尝试解析参数
                                        for (var i = 2; i < args.length && i < 10; i++) {
                                            try {
                                                if (args[i] && !args[i].isNull()) {
                                                    var arg = new ObjC.Object(args[i]);
                                                    var argStr = arg.toString();
                                                    if (argStr.includes('nike') || argStr.includes('complete_daily_sign') || argStr.includes('redeem')) {
                                                        colorLog("[🎯] 发现Nike相关参数: " + argStr.substring(0, 200) + "...", 'yellow');
                                                    }
                                                }
                                            } catch (e) {
                                                // 忽略参数解析错误
                                            }
                                        }
                                    }
                                });
                            } catch (e) {
                                // 忽略Hook失败
                            }
                        }
                    });
                }
            } catch (e) {
                // 忽略类处理错误
            }
        });

        // 2. Hook WebView的evaluateJavaScript方法
        var WKWebView = ObjC.classes.WKWebView;
        if (WKWebView) {
            colorLog("[✓] 找到WKWebView", 'green');

            var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
            if (evaluateJS) {
                Interceptor.attach(evaluateJS.implementation, {
                    onEnter: function(args) {
                        try {
                            var jsCode = new ObjC.Object(args[2]).toString();
                            if (jsCode.includes('nike') ||
                                jsCode.includes('complete_daily_sign') ||
                                jsCode.includes('wx.request') ||
                                jsCode.includes('XMLHttpRequest')) {
                                colorLog("[🌐] WebView执行JS: " + jsCode.substring(0, 200) + "...", 'cyan');
                            }
                        } catch (e) {
                            // 忽略
                        }
                    }
                });
            }
        }

        // 3. Hook所有包含"request"的方法
        var allClasses = Object.keys(ObjC.classes);
        var requestClasses = allClasses.filter(name =>
            name.toLowerCase().includes('request') ||
            name.toLowerCase().includes('network') ||
            name.toLowerCase().includes('http')
        );

        colorLog("[📋] 找到网络相关类: " + requestClasses.length + "个", 'blue');

    } catch (e) {
        colorLog("[-] Hook微信小程序网络层失败: " + e, 'red');
    }
}

// ==================== 3. Hook NSString相关方法查找关键数据 ====================
function hookNSStringForKeywords() {
    try {
        var NSString = ObjC.classes.NSString;
        if (NSString) {
            // Hook stringWithFormat方法来捕获格式化字符串
            var originalStringWithFormat = NSString['+ stringWithFormat:'];
            
            Interceptor.attach(originalStringWithFormat.implementation, {
                onEnter: function(args) {
                    try {
                        var format = new ObjC.Object(args[2]).toString();
                        
                        // 检查是否包含关键词
                        if (format.includes('complete_daily_sign') || 
                            format.includes('wechat_auth') ||
                            format.includes('nike.com') ||
                            format.includes('redeem')) {
                            
                            colorLog("[🔍] 发现关键字符串格式: " + format, 'magenta');
                            
                            // 尝试获取格式化后的结果
                            var result = this.replace(originalStringWithFormat.implementation);
                            if (result) {
                                colorLog("[📝] 格式化结果: " + new ObjC.Object(result).toString(), 'yellow');
                            }
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }
            });
        }
    } catch (e) {
        colorLog("[-] Hook NSString失败: " + e, 'red');
    }
}

// ==================== 4. Hook CFNetwork底层网络请求 ====================
function hookCFNetwork() {
    try {
        // Hook CFHTTPMessageCreateRequest
        var CFHTTPMessageCreateRequest = Module.findExportByName('CFNetwork', 'CFHTTPMessageCreateRequest');
        
        if (CFHTTPMessageCreateRequest) {
            colorLog("[✓] 找到CFHTTPMessageCreateRequest", 'green');
            
            Interceptor.attach(CFHTTPMessageCreateRequest, {
                onEnter: function(args) {
                    try {
                        var method = Memory.readUtf8String(args[1]);
                        var url = Memory.readUtf8String(args[2]);
                        
                        if (url && (url.includes('nike.com') || url.includes('wechat'))) {
                            colorLog("[🌐] CFNetwork请求: " + method + " " + url, 'cyan');
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    } catch (e) {
        colorLog("[-] Hook CFNetwork失败: " + e, 'red');
    }
}

// ==================== 5. Hook内存中的关键字符串 ====================
function hookMemoryStrings() {
    try {
        // 扫描内存中的关键字符串
        var ranges = Process.enumerateRanges('r--');
        
        ranges.forEach(range => {
            try {
                Memory.scan(range.base, range.size, '63 6f 6d 70 6c 65 74 65 5f 64 61 69 6c 79 5f 73 69 67 6e', {
                    onMatch: function(address, size) {
                        colorLog("[🎯] 在内存中找到 'complete_daily_sign' 字符串: " + address, 'green');
                        
                        // 读取周围的内存内容
                        try {
                            var context = Memory.readUtf8String(address.sub(50), 200);
                            colorLog("[📖] 上下文: " + context, 'yellow');
                        } catch (e) {
                            // 忽略
                        }
                    },
                    onError: function(reason) {
                        // 忽略扫描错误
                    }
                });
            } catch (e) {
                // 忽略范围扫描错误
            }
        });
    } catch (e) {
        colorLog("[-] 内存扫描失败: " + e, 'red');
    }
}

// ==================== 初始化所有Hook ====================
function initializeHooks() {
    colorLog("[+] 开始初始化iOS Hook...", 'green');
    
    hookNSURLSession();
    hookWeChatMiniProgramNetwork();
    hookNSStringForKeywords();
    hookCFNetwork();
    
    // 延迟执行内存扫描
    setTimeout(hookMemoryStrings, 5000);
    
    colorLog("\n[✅] iOS Hook初始化完成!", 'green');
    colorLog("[💡] 请在iPhone上打开Nike小程序并执行签到操作", 'yellow');
    colorLog("[📊] 输入 'summary()' 查看捕获数据摘要", 'blue');
    colorLog("=====================================\n", 'white');
}

// ==================== 全局函数 ====================
this.summary = function() {
    colorLog("\n📊 iOS数据捕获摘要", 'cyan');
    colorLog("=====================================", 'white');
    colorLog("网络请求数量: " + hookData.networkRequests.length, 'green');
    colorLog("签到请求数量: " + hookData.signInRequests.length, 'green');
    colorLog("JS-API调用数量: " + hookData.jsApiCalls.length, 'green');
    colorLog("=====================================\n", 'white');
    
    if (hookData.signInRequests.length > 0) {
        colorLog("最新签到请求:", 'yellow');
        console.log(JSON.stringify(hookData.signInRequests[hookData.signInRequests.length - 1], null, 2));
    }
};

this.exportData = function() {
    colorLog("\n💾 导出所有iOS捕获数据", 'cyan');
    colorLog("=====================================", 'white');
    console.log(JSON.stringify(hookData, null, 2));
    colorLog("=====================================\n", 'white');
    return hookData;
};

this.scanMemory = function() {
    colorLog("\n🔍 手动扫描内存中的Nike相关字符串", 'cyan');
    hookMemoryStrings();
};

// 启动Hook
if (ObjC.available) {
    initializeHooks();
} else {
    colorLog("[-] Objective-C运行时不可用，这可能不是iOS应用", 'red');
}

// 定期状态报告
setInterval(function() {
    if (hookData.networkRequests.length > 0 || hookData.signInRequests.length > 0) {
        colorLog("\n[📈] iOS实时状态 - 网络:" + hookData.networkRequests.length + 
                " 签到:" + hookData.signInRequests.length, 'blue');
    }
}, 30000);

colorLog("🍎 iOS脚本加载完成，等待Hook触发...", 'green');
