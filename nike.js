/**
 * Nike小程序自动签到脚本
 * 作者：Tianxx
 * 版本：2.0
 * 日期：2025-07-30
 * 功能：自动获取微信授权码，登录Nike小程序，执行每日签到
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wx096c43d1829a7788'; // Nike小程序appid

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class ScriptTemplate {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;

        // Nike相关配置
        this.nikeConfig = {
            wechatApiUrl: "https://wechat.nike.com.cn",
            nikeApiUrl: "https://api.nike.com.cn", // 回到原始API地址
            accountsUrl: "https://accounts.nike.com.cn",
            appId: "wechat:mp:wx096c43d1829a7788",
            clientId: "5e02c316811ebcb9e6960bc4bdefdaf1"
        };

        // Nike认证信息
        this.nikeAuth = {
            accessToken: null,
            refreshToken: null,
            userId: null,
            expiresIn: null
        };
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    // 恢复Nike认证信息
                    if (userCache.nikeAuth) {
                        this.nikeAuth = userCache.nikeAuth;
                    }

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] 微信Code: ${this.wxCode}`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                cacheExpireTime: expireTime,
                updateTime: Date.now(),
                nikeAuth: this.nikeAuth  // 保存Nike认证信息
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // Nike登录 - 使用微信code获取Nike Token
    async nikeLogin() {
        if (!this.wxCode) {
            console.log(`❌ 缺少微信授权码，无法进行Nike登录`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始Nike登录...`);

        try {
            const response = await this.makeHttpRequest('POST', `${this.nikeConfig.wechatApiUrl}/wechat_auth/token/v1`, {
                appId: this.nikeConfig.appId,
                code: this.wxCode
            });

            if (response && (response.accessToken || response.access_token)) {
                this.nikeAuth.accessToken = response.accessToken || response.access_token;
                this.nikeAuth.refreshToken = response.refreshToken || response.refresh_token;
                this.nikeAuth.userId = response.user_id || response.upmId || this.extractUserIdFromToken(response.accessToken || response.access_token);
                this.nikeAuth.expiresIn = response.expiresIn || response.expires_in;

                if (isDebug) {
                    console.log(`[DEBUG] Nike登录成功`);
                    console.log(`[DEBUG] User ID: ${this.nikeAuth.userId}`);
                    console.log(`[DEBUG] Token过期时间: ${this.nikeAuth.expiresIn}秒`);
                }

                return true;
            } else {
                console.log(`❌ Nike登录失败: 响应格式错误`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Nike登录失败: ${error.message}`);
            return false;
        }
    }

    // 从JWT token中提取用户信息
    extractUserIdFromToken(token) {
        if (!token) return null;

        try {
            // JWT token格式: header.payload.signature
            const parts = token.split('.');
            if (parts.length !== 3) return null;

            // 解码payload部分
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

            if (isDebug) {
                console.log(`[DEBUG] JWT Payload:`, {
                    openId: payload.openId,
                    unionId: payload.unionId,
                    sub: payload.sub,
                    aud: payload.aud
                });
            }

            // 尝试从不同字段获取用户ID
            return payload.openId || payload.unionId || payload.sub || null;
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 解析JWT token失败: ${error.message}`);
            return null;
        }
    }

    // 刷新Nike Token
    async refreshNikeToken() {
        if (!this.nikeAuth.refreshToken) {
            console.log(`❌ 缺少refresh token，无法刷新`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 刷新Nike Token...`);

        try {
            const response = await this.makeHttpRequest('POST', `${this.nikeConfig.accountsUrl}/token/v1`, {
                refresh_token: this.nikeAuth.refreshToken,
                client_id: this.nikeConfig.clientId,
                grant_type: "refresh_token"
            }, {
                'Content-Type': 'application/x-www-form-urlencoded'
            }, true);

            if (response && (response.accessToken || response.access_token)) {
                this.nikeAuth.accessToken = response.accessToken || response.access_token;
                this.nikeAuth.expiresIn = response.expiresIn || response.expires_in;

                if (isDebug) console.log(`[DEBUG] Token刷新成功`);
                return true;
            } else {
                console.log(`❌ Token刷新失败: 响应格式错误`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Token刷新失败: ${error.message}`);
            return false;
        }
    }

    // Nike每日签到
    async nikeDailySignIn() {
        if (!this.nikeAuth.accessToken || !this.nikeAuth.userId) {
            console.log(`❌ 缺少Nike认证信息，无法签到`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始Nike每日签到...`);

        try {
            const response = await this.makeHttpRequest('GET',
                `${this.nikeConfig.nikeApiUrl}/onemp/redeem/complete_daily_sign/v2/${this.nikeAuth.userId}`,
                null,
                {
                    'Authorization': `Bearer ${this.nikeAuth.accessToken}`,
                    'App-Id': this.nikeConfig.appId
                }
            );

            if (response) {
                // 检查是否是阿里云错误
                if (typeof response === 'string' && response.includes('NoSuchBucket')) {
                    console.log(`⚠️ Nike API需要通过微信云开发代理访问`);
                    console.log(`💡 建议使用Frida hook微信小程序获取真实的签到请求`);
                    return { success: false, error: 'API_PROXY_REQUIRED', message: '需要云开发代理' };
                }

                const points = response.points || 0;
                console.log(`🎉 Nike签到成功! 获得 ${points} 积分`);
                return { success: true, points: points, data: response };
            } else {
                console.log(`❌ Nike签到失败: 响应为空`);
                return false;
            }
        } catch (error) {
            if (error.message.includes('401')) {
                console.log(`⚠️ Token可能已过期，尝试刷新...`);
                const refreshSuccess = await this.refreshNikeToken();
                if (refreshSuccess) {
                    // 递归重试签到
                    return await this.nikeDailySignIn();
                } else {
                    console.log(`❌ Token刷新失败，需要重新登录`);
                    return false;
                }
            } else {
                console.log(`❌ Nike签到失败: ${error.message}`);
                return false;
            }
        }
    }

    // 获取Nike兑换中心信息
    async getNikeRedeemInfo() {
        if (!this.nikeAuth.accessToken) {
            console.log(`❌ 缺少Nike认证信息`);
            return null;
        }

        try {
            const response = await this.makeHttpRequest('GET',
                `${this.nikeConfig.nikeApiUrl}/onemp/redeem/redeem_center_info/v2`,
                null,
                {
                    'Authorization': `Bearer ${this.nikeAuth.accessToken}`,
                    'App-Id': this.nikeConfig.appId
                }
            );

            if (isDebug) console.log(`[DEBUG] 获取兑换中心信息成功`);
            return response;
        } catch (error) {
            console.log(`❌ 获取兑换中心信息失败: ${error.message}`);
            return null;
        }
    }

    // 获取Nike积分日志
    async getNikePointsLog() {
        if (!this.nikeAuth.accessToken) {
            console.log(`❌ 缺少Nike认证信息`);
            return null;
        }

        try {
            const response = await this.makeHttpRequest('GET',
                `${this.nikeConfig.nikeApiUrl}/onemp/redeem/points_log/v1`,
                null,
                {
                    'Authorization': `Bearer ${this.nikeAuth.accessToken}`,
                    'App-Id': this.nikeConfig.appId
                }
            );

            if (isDebug) console.log(`[DEBUG] 获取积分日志成功`);
            return response;
        } catch (error) {
            console.log(`❌ 获取积分日志失败: ${error.message}`);
            return null;
        }
    }

    // HTTP请求封装
    async makeHttpRequest(method, url, data = null, customHeaders = {}, isFormData = false) {
        if (isFormData && customHeaders['Content-Type']) {
            // 如果是表单数据，设置正确的Content-Type
            customHeaders['Content-Type'] = 'application/x-www-form-urlencoded';
        }

        try {
            const response = await wxcode.httpRequest(method, url, data, customHeaders);

            // 解析JSON响应
            let parsedResponse = response;
            if (typeof response === 'string') {
                try {
                    parsedResponse = JSON.parse(response);
                } catch (e) {
                    // 如果不是JSON格式，直接返回原始响应
                    parsedResponse = response;
                }
            }

            // 检查是否有错误
            if (parsedResponse && (parsedResponse.error || parsedResponse.errcode)) {
                throw new Error(parsedResponse.error || parsedResponse.errmsg || '请求失败');
            }

            return parsedResponse;
        } catch (error) {
            throw error;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.wxCode) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试获取一个简单的信息来验证登录状态
            const testResult = await wxcode.getOpenid(this.wxid, this.appid);
            if (testResult.success) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 执行完整的数据获取流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的数据获取流程...`);

        // 1. 获取授权码并登录
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. Nike登录
        const nikeLoginSuccess = await this.nikeLogin();
        if (!nikeLoginSuccess) {
            console.log(`[${this.wxid}] Nike登录失败`);
            return false;
        }

        // 3. 保存到缓存
        this.saveTokenCache();

        return true;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. 执行Nike签到
            console.log(`🎯 开始执行Nike每日签到...`);
            const signInResult = await this.nikeDailySignIn();

            if (signInResult && signInResult.success) {
                const message = `✅ [${this.wxid}] Nike签到成功! 获得 ${signInResult.points} 积分`;
                console.log(message);
                print(message, true);

                // 获取积分日志（可选）
                if (isDebug) {
                    const pointsLog = await this.getNikePointsLog();
                    if (pointsLog) {
                        console.log(`[DEBUG] 积分日志:`, pointsLog);
                    }
                }
            } else {
                const message = `❌ [${this.wxid}] Nike签到失败`;
                console.log(message);
                print(message, true);
            }
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 Nike小程序自动签到脚本开始执行`);

    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] Nike小程序APPID: ${APPID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new ScriptTemplate(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
