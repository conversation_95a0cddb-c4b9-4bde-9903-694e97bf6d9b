/**
 * Nike小程序通用云函数抓取脚本
 * 捕获所有云函数调用、网络请求和JavaScript执行
 * 适用于测试任何Nike小程序功能
 */

console.log("🎯 Nike通用抓取脚本 v2.0");
console.log("=====================================");
console.log("📱 目标: 捕获所有云函数调用");
console.log("🔍 范围: 签到、积分、任务、兑换等所有功能");
console.log("=====================================\n");

var capturedData = {
    cloudFunctions: [],
    httpRequests: [],
    jsExecutions: [],
    nikeAPIs: []
};

var stats = {
    totalCalls: 0,
    cloudCalls: 0,
    httpCalls: 0,
    jsCalls: 0
};

// 颜色输出
function log(message, color = 'white') {
    const colors = {
        red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m',
        blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m',
        white: '\x1b[37m', reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

// 检查是否是Nike相关的调用
function isNikeRelated(content) {
    const keywords = [
        'nike', 'redeem', 'complete_daily_sign', 'points', 'reward',
        'task', 'mission', 'prize', 'coupon', 'discount', 'signin',
        'checkin', 'daily', 'bonus', 'earn', 'exchange', 'gift',
        'api_name', 'CloudCallFunction', 'callContainer', 'wx.cloud',
        'onemp', 'wechat_auth', 'token', 'user_task', 'recent_actions'
    ];
    
    const lowerContent = content.toLowerCase();
    return keywords.some(keyword => lowerContent.includes(keyword));
}

// 提取关键信息
function extractKeyInfo(content) {
    const info = {};
    
    // 提取API名称
    const apiMatch = content.match(/"api_name"\s*:\s*"([^"]+)"/);
    if (apiMatch) info.apiName = apiMatch[1];
    
    // 提取路径
    const pathMatch = content.match(/"path"\s*:\s*"([^"]+)"/);
    if (pathMatch) info.path = pathMatch[1];
    
    // 提取方法
    const methodMatch = content.match(/"method"\s*:\s*"([^"]+)"/);
    if (methodMatch) info.method = methodMatch[1];
    
    // 提取Token
    const tokenMatch = content.match(/Bearer\s+([a-zA-Z0-9\-_.]+)/);
    if (tokenMatch) info.token = tokenMatch[1].substring(0, 30) + '...';
    
    // 提取用户ID
    const userIdMatch = content.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/);
    if (userIdMatch) info.userId = userIdMatch[0];
    
    return info;
}

if (ObjC.available) {
    // Hook WebView JavaScript执行
    var WKWebView = ObjC.classes.WKWebView;
    if (WKWebView) {
        var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
        if (evaluateJS) {
            Interceptor.attach(evaluateJS.implementation, {
                onEnter: function(args) {
                    try {
                        var jsCode = new ObjC.Object(args[2]).toString();
                        
                        if (isNikeRelated(jsCode)) {
                            stats.jsCalls++;
                            stats.totalCalls++;
                            
                            var keyInfo = extractKeyInfo(jsCode);
                            var logLevel = jsCode.includes('complete_daily_sign') ? 'green' : 'cyan';
                            
                            log(`\n[📱 JS执行 #${stats.jsCalls}] ${new Date().toLocaleTimeString()}`, logLevel);
                            
                            if (keyInfo.apiName) log(`🎯 API: ${keyInfo.apiName}`, 'yellow');
                            if (keyInfo.path) log(`📍 路径: ${keyInfo.path}`, 'blue');
                            if (keyInfo.method) log(`🔧 方法: ${keyInfo.method}`, 'magenta');
                            if (keyInfo.userId) log(`👤 用户: ${keyInfo.userId}`, 'cyan');
                            
                            log(`📝 代码片段: ${jsCode.substring(0, 150)}...`, 'white');
                            
                            // 保存数据
                            capturedData.jsExecutions.push({
                                id: stats.jsCalls,
                                timestamp: new Date().toISOString(),
                                keyInfo: keyInfo,
                                fullCode: jsCode,
                                type: 'javascript_execution'
                            });
                            
                            // 特别标记签到相关
                            if (jsCode.includes('complete_daily_sign')) {
                                log("🎉 这是签到相关调用！", 'green');
                            }
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    }
    
    // Hook NSURLSession网络请求
    var NSURLSession = ObjC.classes.NSURLSession;
    if (NSURLSession) {
        var dataTask = NSURLSession['- dataTaskWithRequest:completionHandler:'];
        if (dataTask) {
            Interceptor.attach(dataTask.implementation, {
                onEnter: function(args) {
                    try {
                        var request = new ObjC.Object(args[2]);
                        var url = request.URL().absoluteString().toString();
                        
                        // 捕获所有可能相关的请求
                        if (url.includes('nike.com') || 
                            url.includes('CloudCallFunction') ||
                            url.includes('container_service') ||
                            url.includes('servicewechat.com') ||
                            url.includes('wxa-qbase') ||
                            isNikeRelated(url)) {
                            
                            stats.httpCalls++;
                            stats.totalCalls++;
                            
                            log(`\n[🌐 HTTP请求 #${stats.httpCalls}] ${new Date().toLocaleTimeString()}`, 'blue');
                            log(`🔗 URL: ${url}`, 'cyan');
                            
                            var requestData = {
                                id: stats.httpCalls,
                                timestamp: new Date().toISOString(),
                                url: url,
                                method: request.HTTPMethod().toString(),
                                headers: {},
                                body: null,
                                type: 'http_request'
                            };
                            
                            // 获取请求头
                            try {
                                var allHeaders = request.allHTTPHeaderFields();
                                if (allHeaders) {
                                    var keys = allHeaders.allKeys();
                                    for (var i = 0; i < keys.count(); i++) {
                                        var key = keys.objectAtIndex_(i).toString();
                                        var value = allHeaders.objectForKey_(key).toString();
                                        requestData.headers[key] = value;
                                    }
                                    log(`📋 请求头数量: ${keys.count()}`, 'magenta');
                                }
                            } catch (e) {
                                log("❌ 无法读取请求头", 'red');
                            }
                            
                            // 获取请求体
                            var httpBody = request.HTTPBody();
                            if (httpBody) {
                                try {
                                    var bodyData = new ObjC.Object(httpBody);
                                    var bodyString = bodyData.toString();
                                    requestData.body = bodyString;
                                    
                                    log(`📦 请求体长度: ${bodyString.length}`, 'yellow');
                                    
                                    // 尝试解析JSON
                                    try {
                                        var jsonData = JSON.parse(bodyString);
                                        if (jsonData.data) {
                                            log("🎯 发现data字段!", 'green');
                                            log(`📊 Data内容: ${jsonData.data.substring(0, 100)}...`, 'yellow');
                                            
                                            // 这是云函数调用！
                                            stats.cloudCalls++;
                                            capturedData.cloudFunctions.push({
                                                id: stats.cloudCalls,
                                                timestamp: new Date().toISOString(),
                                                url: url,
                                                requestData: jsonData,
                                                type: 'cloud_function'
                                            });
                                            
                                            log("☁️ 这是云函数调用！", 'green');
                                        }
                                    } catch (e) {
                                        log(`📦 原始请求体: ${bodyString.substring(0, 200)}...`, 'white');
                                    }
                                } catch (e) {
                                    log("❌ 无法读取请求体", 'red');
                                }
                            }
                            
                            // 保存HTTP请求数据
                            capturedData.httpRequests.push(requestData);
                            
                            // 特别标记重要请求
                            if (url.includes('CloudCallFunction')) {
                                log("🎉 这是云函数调用请求！", 'green');
                            } else if (url.includes('nike.com')) {
                                log("🏃 这是Nike API请求！", 'yellow');
                            }
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
    }
    
    log("✅ 通用Hook设置完成", 'green');
    log("💡 现在可以在Nike小程序中进行任何操作:", 'yellow');
    log("  - 查看积分记录", 'white');
    log("  - 浏览兑换商品", 'white');
    log("  - 查看任务列表", 'white');
    log("  - 点击签到按钮", 'white');
    log("  - 兑换优惠券", 'white');
    log("  - 查看个人资料", 'white');
    log("=====================================\n", 'white');
}

// 实时状态显示
setInterval(function() {
    if (stats.totalCalls > 0) {
        log(`\n📈 实时统计 - 总计:${stats.totalCalls} | JS:${stats.jsCalls} | HTTP:${stats.httpCalls} | 云函数:${stats.cloudCalls}`, 'blue');
    }
}, 15000);

// 全局函数 - 显示统计信息
this.showStats = function() {
    log("\n📊 捕获统计", 'cyan');
    log("=====================================", 'white');
    log(`总调用次数: ${stats.totalCalls}`, 'green');
    log(`JavaScript执行: ${stats.jsCalls}`, 'yellow');
    log(`HTTP请求: ${stats.httpCalls}`, 'blue');
    log(`云函数调用: ${stats.cloudCalls}`, 'magenta');
    log("=====================================\n", 'white');
    return stats;
};

// 全局函数 - 显示所有捕获数据
this.showAllData = function() {
    log("\n📋 所有捕获数据", 'cyan');
    log("=====================================", 'white');
    
    if (capturedData.cloudFunctions.length > 0) {
        log(`\n☁️ 云函数调用 (${capturedData.cloudFunctions.length}个):`, 'green');
        capturedData.cloudFunctions.forEach(item => {
            log(`[${item.id}] ${item.timestamp}`, 'yellow');
            log(`URL: ${item.url}`, 'cyan');
            log(`Data: ${JSON.stringify(item.requestData).substring(0, 200)}...`, 'white');
        });
    }
    
    if (capturedData.jsExecutions.length > 0) {
        log(`\n📱 JavaScript执行 (${capturedData.jsExecutions.length}个):`, 'blue');
        capturedData.jsExecutions.forEach(item => {
            log(`[${item.id}] ${item.timestamp}`, 'yellow');
            if (item.keyInfo.apiName) log(`API: ${item.keyInfo.apiName}`, 'cyan');
            if (item.keyInfo.path) log(`路径: ${item.keyInfo.path}`, 'magenta');
        });
    }
    
    return capturedData;
};

// 全局函数 - 导出云函数数据
this.exportCloudFunctions = function() {
    if (capturedData.cloudFunctions.length === 0) {
        log("❌ 暂无云函数调用数据", 'red');
        return null;
    }
    
    log("\n☁️ 云函数调用数据:", 'green');
    capturedData.cloudFunctions.forEach((item, index) => {
        log(`\n[云函数 ${index + 1}] ${item.timestamp}`, 'yellow');
        log("完整请求数据:", 'cyan');
        console.log(JSON.stringify(item.requestData, null, 2));
        
        log("\n📋 对应的curl命令:", 'blue');
        log(`curl -X POST "http://820121.xyz:8800/api/Wxapp/CloudCallFunction" \\`, 'white');
        log(`  -H "accept: application/json" \\`, 'white');
        log(`  -H "content-type: application/json" \\`, 'white');
        log(`  -d '${JSON.stringify(item.requestData)}'`, 'white');
    });
    
    return capturedData.cloudFunctions;
};

// 全局函数 - 查找特定功能
this.findFunction = function(keyword) {
    keyword = keyword.toLowerCase();
    var results = [];
    
    // 搜索云函数
    capturedData.cloudFunctions.forEach(item => {
        if (JSON.stringify(item).toLowerCase().includes(keyword)) {
            results.push({type: 'cloud_function', data: item});
        }
    });
    
    // 搜索JS执行
    capturedData.jsExecutions.forEach(item => {
        if (item.fullCode.toLowerCase().includes(keyword)) {
            results.push({type: 'javascript', data: item});
        }
    });
    
    // 搜索HTTP请求
    capturedData.httpRequests.forEach(item => {
        if (JSON.stringify(item).toLowerCase().includes(keyword)) {
            results.push({type: 'http_request', data: item});
        }
    });
    
    log(`\n🔍 搜索 "${keyword}" 的结果 (${results.length}个):`, 'cyan');
    results.forEach((result, index) => {
        log(`\n[${index + 1}] ${result.type}`, 'yellow');
        if (result.type === 'cloud_function') {
            log(`时间: ${result.data.timestamp}`, 'white');
            log(`URL: ${result.data.url}`, 'blue');
            log(`Data: ${JSON.stringify(result.data.requestData).substring(0, 150)}...`, 'white');
        } else if (result.type === 'javascript') {
            log(`时间: ${result.data.timestamp}`, 'white');
            if (result.data.keyInfo.apiName) log(`API: ${result.data.keyInfo.apiName}`, 'cyan');
            log(`代码: ${result.data.fullCode.substring(0, 150)}...`, 'white');
        }
    });
    
    return results;
};

log("🎯 通用抓取脚本已就绪，开始监控Nike小程序...", 'green');
log("💡 可用命令:", 'yellow');
log("  showStats() - 显示统计信息", 'white');
log("  showAllData() - 显示所有数据", 'white');
log("  exportCloudFunctions() - 导出云函数数据", 'white');
log("  findFunction('关键词') - 搜索特定功能", 'white');
