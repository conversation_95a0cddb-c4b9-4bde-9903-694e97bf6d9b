{"timestamp": "2025-07-31T09:25:17.125Z", "success": false, "error": "解析用户信息失败: The first argument must be of type string or an instance of <PERSON><PERSON><PERSON>, <PERSON><PERSON>y<PERSON>uff<PERSON>, or Array or an Array-like Object. Received undefined", "cloudCallInfo": {"path": "/onemp/redeem/complete_daily_sign/v2/wxid_ltkystdcspc822", "method": "GET", "header": {"X-WX-EXCLUDE-CREDENTIALS": "cloudbase-access-token", "X-WX-GATEWAY-ID": "test-8g5tq0ha6215e83c", "HOST": "api.nike.com.cn", "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEifQ.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6Ik5pa2UgVXNlciIsImlhdCI6MTUxNjIzOTAyMiwiZXhwIjoxNzUzODUzODAwLCJvcGVuSWQiOiJ3eGlkX2x0a3lzdGRjc3BjODIyIiwidW5pb25JZCI6InVuaW9uSWRfZXhhbXBsZSIsImF1ZCI6Ind4MDk2YzQzZDE4MjlhNzc4OCIsImlzcyI6Im5pa2UuY29tLmNuIn0.example_signature_here", "App-Id": "wechat:mp:wx096c43d1829a7788", "Accept": "application/json", "Content-Type": "application/json; charset=UTF-8", "nike-api-caller-id": "nike:wechat:web:1.0"}}}