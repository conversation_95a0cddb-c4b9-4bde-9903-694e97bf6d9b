# Nike小程序自动签到 - 明天执行方案

## 🎯 目标
获取Nike小程序签到的真实云函数调用参数，实现自动签到

## 📋 准备工作

### 1. 确认文件完整性
- ✅ `simple_nike_hook.js` - 简化的Frida Hook脚本
- ✅ `nike_tokens.json` - Nike认证信息
- ✅ `wxcode.js` - 微信登录模块

### 2. 设备准备
- iPhone连接电脑
- 微信已安装并登录
- Nike小程序可正常使用

## 🚀 执行步骤

### 第一步：启动Frida Hook
```bash
cd "C:\Users\<USER>\Desktop\cursor\微信小程序\nike"
frida -U 微信 -l simple_nike_hook.js
```

### 第二步：在iPhone上操作
1. 打开微信
2. 进入Nike小程序
3. 进入"福利中心"
4. **点击签到按钮**
5. 等待签到完成

### 第三步：查看Frida输出
在Frida控制台中应该会看到：
```
🎉 发现签到相关调用!
==================================================
JS代码:
wx.cloud.callContainer({
  path: "/onemp/redeem/complete_daily_sign/v2/用户ID",
  method: "GET",
  header: {
    "Authorization": "Bearer 真实token",
    "X-WX-EXCLUDE-CREDENTIALS": "cloudbase-access-token",
    "X-WX-GATEWAY-ID": "真实网关ID",
    // ... 其他重要参数
  }
})
==================================================
```

### 第四步：保存真实参数
将Frida捕获的真实参数保存到 `nike_real_cloud_params.json`

### 第五步：创建最终签到脚本
基于真实参数创建可用的自动签到脚本

## 🔍 关键信息要捕获

### 1. 云函数调用参数
- `path` - 真实的API路径
- `method` - HTTP方法
- `header` - 完整的请求头

### 2. 认证信息
- `Authorization` - Bearer token格式
- `X-WX-EXCLUDE-CREDENTIALS` - 云开发凭据
- `X-WX-GATEWAY-ID` - 网关ID

### 3. 用户标识
- 真实的用户ID格式
- 可能不是wxid，而是其他格式

### 4. 响应数据
- 签到成功的响应格式
- 积分数据结构

## 🛠️ 备用方案

如果Frida Hook失败：

### 方案A：抓包分析
使用Charles或mitmproxy抓取HTTPS请求

### 方案B：内存分析
使用更深入的内存分析工具

### 方案C：逆向分析
深入分析小程序的编译后代码

## 📊 预期结果

成功后我们将获得：
1. **完整的云函数调用代码**
2. **可用的自动签到脚本**
3. **每日自动执行的定时任务**

## 🎉 最终目标

创建一个可以每天自动执行的Nike签到脚本：
```bash
# 每天早上8点自动执行
node nike_auto_signin.js
```

输出：
```
🎉 Nike签到成功! 获得 10 积分
💰 当前总积分: 1250
```

---

## 📝 注意事项

1. **时间窗口** - 签到通常在每天0点重置
2. **网络环境** - 确保网络稳定
3. **Token有效期** - 注意Token过期时间
4. **错误处理** - 准备好错误重试机制

## 🔧 调试工具

如果遇到问题，可以使用：
- `nike_api_test.js` - 测试API连通性
- `nike_cloud_signin.js` - 测试云函数调用
- Frida Hook脚本 - 实时监控调用

---

**明天见！🚀**
