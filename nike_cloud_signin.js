/**
 * Nike云函数签到脚本
 * 模拟wx.cloud.callContainer调用
 * 基于小程序源码分析
 */

const fs = require('fs');

// 从小程序源码分析得到的云函数配置
const CLOUD_CONFIG = {
    // 从config.js获取的云开发配置
    envId: "test-52toys-7gc5xgy7a72c959f",
    excludeCredentials: "cloudbase-access-token",
    gatewayId: "test-8g5tq0ha6215e83c",
    
    // 从HttpClient.js分析的调用参数
    containerParams: {
        path: "/onemp/redeem/complete_daily_sign/v2/{userId}",
        method: "GET",
        header: {
            "X-WX-EXCLUDE-CREDENTIALS": "cloudbase-access-token",
            "X-WX-GATEWAY-ID": "test-8g5tq0ha6215e83c", 
            "HOST": "api.nike.com.cn",
            "Authorization": "Bearer {token}",
            "App-Id": "wechat:mp:wx096c43d1829a7788",
            "Accept": "application/json",
            "Content-Type": "application/json; charset=UTF-8",
            "nike-api-caller-id": "nike:wechat:web:1.0"
        }
    }
};

// 日志函数
const log = (message, type = 'info') => {
    const colors = {
        info: '\x1b[37m', success: '\x1b[32m', warning: '\x1b[33m',
        error: '\x1b[31m', debug: '\x1b[36m', reset: '\x1b[0m'
    };
    console.log(`${colors[type]}${message}${colors.reset}`);
};

// 加载认证信息
function loadAuth() {
    try {
        const data = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
        log(`✅ 加载认证信息: ${data.userId || data.wxid}`, 'success');
        return data;
    } catch (error) {
        log(`❌ 无法加载认证信息: ${error.message}`, 'error');
        return null;
    }
}

// 使用wxcode模拟云函数调用
async function callCloudFunction(auth) {
    log('🌐 开始模拟wx.cloud.callContainer调用...', 'info');
    
    try {
        const wxcode = require('./wxcode');
        
        // 构造云函数调用参数
        const userId = auth.userId || auth.wxid;
        const containerParams = {
            ...CLOUD_CONFIG.containerParams,
            path: CLOUD_CONFIG.containerParams.path.replace('{userId}', userId),
            header: {
                ...CLOUD_CONFIG.containerParams.header,
                "Authorization": `Bearer ${auth.accessToken}`
            }
        };
        
        log('📋 云函数调用参数:', 'debug');
        log(JSON.stringify(containerParams, null, 2), 'debug');
        
        // 构造wxcode调用数据
        const wxcodeData = {
            action: 'wx_cloud_call_container',
            envId: CLOUD_CONFIG.envId,
            containerParams: containerParams
        };
        
        log('🚀 发起wxcode云函数调用...', 'info');
        
        const result = await wxcode.getUserInfo(
            auth.wxid,
            'wx096c43d1829a7788',
            JSON.stringify(wxcodeData)
        );
        
        if (result.success) {
            log('✅ 云函数调用成功!', 'success');
            log(`📥 原始响应: ${JSON.stringify(result)}`, 'debug');
            
            // 尝试解析签到结果
            try {
                let signInData;
                if (typeof result.rawData === 'string') {
                    signInData = JSON.parse(result.rawData);
                } else {
                    signInData = result.rawData;
                }
                
                if (signInData && signInData.points !== undefined) {
                    log(`🎉 签到成功! 获得 ${signInData.points} 积分`, 'success');
                    return { success: true, points: signInData.points, data: signInData };
                } else {
                    log(`📊 云函数响应: ${JSON.stringify(signInData)}`, 'info');
                    return { success: true, data: signInData };
                }
            } catch (e) {
                log(`📊 原始响应数据: ${result.rawData}`, 'info');
                return { success: true, data: result.rawData };
            }
        } else {
            log(`❌ 云函数调用失败: ${result.error}`, 'error');
            return { success: false, error: result.error };
        }
    } catch (error) {
        log(`❌ 云函数调用异常: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

// 生成完整的云函数调用信息
function generateCloudCallInfo(auth) {
    const userId = auth.userId || auth.wxid;
    const containerParams = {
        ...CLOUD_CONFIG.containerParams,
        path: CLOUD_CONFIG.containerParams.path.replace('{userId}', userId),
        header: {
            ...CLOUD_CONFIG.containerParams.header,
            "Authorization": `Bearer ${auth.accessToken}`
        }
    };
    
    log('\n📋 完整的云函数调用信息', 'info');
    log('=====================================', 'info');
    log('🌐 微信小程序中的调用代码:', 'info');
    log(`wx.cloud.callContainer(${JSON.stringify(containerParams, null, 2)})`, 'debug');
    
    log('\n🔧 云开发配置:', 'info');
    log(`环境ID: ${CLOUD_CONFIG.envId}`, 'debug');
    log(`网关ID: ${CLOUD_CONFIG.gatewayId}`, 'debug');
    log(`排除凭据: ${CLOUD_CONFIG.excludeCredentials}`, 'debug');
    
    log('\n📡 等效的HTTP请求:', 'info');
    log(`POST https://tcb-api.tencentcloudapi.com/web`, 'debug');
    log(`Headers: X-WX-EXCLUDE-CREDENTIALS, X-WX-GATEWAY-ID, Authorization`, 'debug');
    log(`Body: ${JSON.stringify({ path: containerParams.path, method: containerParams.method, header: containerParams.header })}`, 'debug');
    
    log('=====================================\n', 'info');
    
    return containerParams;
}

// 主函数
async function main() {
    log('🚀 Nike云函数签到脚本', 'info');
    log('=====================================', 'info');
    
    // 1. 加载认证信息
    const auth = loadAuth();
    if (!auth || !auth.accessToken) {
        log('❌ 缺少有效的认证信息', 'error');
        log('💡 请确保nike_tokens.json包含有效的accessToken', 'warning');
        return;
    }
    
    // 2. 生成云函数调用信息
    const cloudCallInfo = generateCloudCallInfo(auth);
    
    // 3. 执行云函数调用
    log('🎯 开始执行Nike签到...', 'info');
    const result = await callCloudFunction(auth);
    
    if (result.success) {
        log('🎉 签到操作完成!', 'success');
        if (result.points) {
            log(`💰 获得积分: ${result.points}`, 'success');
        }
        
        // 保存结果
        const resultData = {
            timestamp: new Date().toISOString(),
            success: true,
            points: result.points,
            data: result.data,
            cloudCallInfo: cloudCallInfo
        };
        
        fs.writeFileSync('./nike_signin_result.json', JSON.stringify(resultData, null, 2));
        log('💾 签到结果已保存到 nike_signin_result.json', 'info');
    } else {
        log('❌ 签到失败', 'error');
        log(`错误信息: ${result.error}`, 'error');
        
        // 保存错误信息用于调试
        const errorData = {
            timestamp: new Date().toISOString(),
            success: false,
            error: result.error,
            cloudCallInfo: cloudCallInfo
        };
        
        fs.writeFileSync('./nike_signin_error.json', JSON.stringify(errorData, null, 2));
        log('💾 错误信息已保存到 nike_signin_error.json', 'info');
    }
    
    log('\n🏁 脚本执行完成', 'info');
    log('💡 如果签到失败，明天可以用Frida抓取真实的云函数调用参数', 'warning');
}

// 执行脚本
if (require.main === module) {
    main().catch(error => {
        log(`❌ 脚本执行出错: ${error.message}`, 'error');
        console.error(error.stack);
    });
}

module.exports = {
    callCloudFunction,
    generateCloudCallInfo,
    CLOUD_CONFIG
};
