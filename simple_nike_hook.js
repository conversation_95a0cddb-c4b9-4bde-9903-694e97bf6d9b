/**
 * 极简Nike签到Hook脚本
 * 专门抓取wx.cloud.callContainer调用
 */

console.log("🎯 Nike签到Hook - 等待签到操作...");

// 只Hook WebView的JavaScript执行
if (ObjC.available) {
    var WKWebView = ObjC.classes.WKWebView;
    if (WKWebView) {
        var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
        if (evaluateJS) {
            Interceptor.attach(evaluateJS.implementation, {
                onEnter: function(args) {
                    try {
                        var jsCode = new ObjC.Object(args[2]).toString();
                        
                        // 只关注包含签到相关的JS代码
                        if (jsCode.includes('complete_daily_sign') || 
                            jsCode.includes('callContainer') ||
                            jsCode.includes('redeem')) {
                            
                            console.log("\n🎉 发现签到相关调用!");
                            console.log("=" * 50);
                            console.log("JS代码:");
                            console.log(jsCode);
                            console.log("=" * 50);
                        }
                    } catch (e) {
                        // 忽略
                    }
                }
            });
        }
        console.log("✅ Hook设置完成，请在小程序中点击签到");
    }
}

// 全局函数 - 手动触发数据导出
this.exportData = function() {
    console.log("请先在小程序中执行签到操作");
};
