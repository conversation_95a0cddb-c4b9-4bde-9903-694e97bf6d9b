/**
 * Nike签到脚本测试文件
 * 用于测试Nike登录和签到功能
 */

const wxcode = require('./wxcode');

// 测试配置
const TEST_CONFIG = {
    wxid: 'test_wxid',  // 替换为你的微信ID
    appid: 'wx096c43d1829a7788',  // Nike小程序ID
    nikeConfig: {
        wechatApiUrl: "https://wechat.nike.com.cn",
        nikeApiUrl: "https://api.nike.com.cn", 
        accountsUrl: "https://accounts.nike.com.cn",
        appId: "wechat:mp:wx096c43d1829a7788",
        clientId: "5e02c316811ebcb9e6960bc4bdefdaf1"
    }
};

// 测试获取微信授权码
async function testGetWxCode() {
    console.log('🧪 测试获取微信授权码...');
    
    try {
        const result = await wxcode.getWxCode(TEST_CONFIG.wxid, TEST_CONFIG.appid);
        
        if (result.success) {
            console.log('✅ 获取微信授权码成功');
            console.log('Code:', result.code);
            return result.code;
        } else {
            console.log('❌ 获取微信授权码失败:', result.error);
            return null;
        }
    } catch (error) {
        console.log('❌ 获取微信授权码异常:', error.message);
        return null;
    }
}

// 测试Nike登录
async function testNikeLogin(wxCode) {
    console.log('🧪 测试Nike登录...');
    
    if (!wxCode) {
        console.log('❌ 缺少微信授权码，跳过Nike登录测试');
        return null;
    }
    
    try {
        const response = await wxcode.httpRequest('POST', `${TEST_CONFIG.nikeConfig.wechatApiUrl}/wechat_auth/token/v1`, {
            appId: TEST_CONFIG.nikeConfig.appId,
            code: wxCode
        });

        if (response && response.access_token) {
            console.log('✅ Nike登录成功');
            console.log('Access Token:', response.access_token.substring(0, 20) + '...');
            console.log('User ID:', response.user_id || response.upmId);
            console.log('Expires In:', response.expires_in, '秒');
            
            return {
                accessToken: response.access_token,
                refreshToken: response.refresh_token,
                userId: response.user_id || response.upmId,
                expiresIn: response.expires_in
            };
        } else {
            console.log('❌ Nike登录失败: 响应格式错误');
            console.log('响应:', response);
            return null;
        }
    } catch (error) {
        console.log('❌ Nike登录异常:', error.message);
        return null;
    }
}

// 测试Nike签到
async function testNikeSignIn(nikeAuth) {
    console.log('🧪 测试Nike签到...');
    
    if (!nikeAuth || !nikeAuth.accessToken || !nikeAuth.userId) {
        console.log('❌ 缺少Nike认证信息，跳过签到测试');
        return false;
    }
    
    try {
        const response = await wxcode.httpRequest('GET', 
            `${TEST_CONFIG.nikeConfig.nikeApiUrl}/onemp/redeem/complete_daily_sign/v2/${nikeAuth.userId}`, 
            null, 
            {
                'Authorization': `Bearer ${nikeAuth.accessToken}`,
                'App-Id': TEST_CONFIG.nikeConfig.appId
            }
        );

        if (response) {
            const points = response.points || 0;
            console.log('✅ Nike签到成功!');
            console.log('获得积分:', points);
            console.log('响应数据:', response);
            return true;
        } else {
            console.log('❌ Nike签到失败: 响应为空');
            return false;
        }
    } catch (error) {
        console.log('❌ Nike签到异常:', error.message);
        
        if (error.message.includes('401')) {
            console.log('💡 提示: Token可能已过期，实际使用时脚本会自动刷新Token');
        }
        
        return false;
    }
}

// 测试获取兑换中心信息
async function testGetRedeemInfo(nikeAuth) {
    console.log('🧪 测试获取兑换中心信息...');
    
    if (!nikeAuth || !nikeAuth.accessToken) {
        console.log('❌ 缺少Nike认证信息，跳过测试');
        return null;
    }
    
    try {
        const response = await wxcode.httpRequest('GET', 
            `${TEST_CONFIG.nikeConfig.nikeApiUrl}/onemp/redeem/redeem_center_info/v2`, 
            null, 
            {
                'Authorization': `Bearer ${nikeAuth.accessToken}`,
                'App-Id': TEST_CONFIG.nikeConfig.appId
            }
        );

        if (response) {
            console.log('✅ 获取兑换中心信息成功');
            console.log('响应数据:', JSON.stringify(response, null, 2));
            return response;
        } else {
            console.log('❌ 获取兑换中心信息失败: 响应为空');
            return null;
        }
    } catch (error) {
        console.log('❌ 获取兑换中心信息异常:', error.message);
        return null;
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始Nike签到功能测试');
    console.log('测试配置:', {
        wxid: TEST_CONFIG.wxid,
        appid: TEST_CONFIG.appid
    });
    console.log('─'.repeat(50));
    
    // 1. 测试获取微信授权码
    const wxCode = await testGetWxCode();
    console.log('─'.repeat(50));
    
    // 2. 测试Nike登录
    const nikeAuth = await testNikeLogin(wxCode);
    console.log('─'.repeat(50));
    
    // 3. 测试Nike签到
    await testNikeSignIn(nikeAuth);
    console.log('─'.repeat(50));
    
    // 4. 测试获取兑换中心信息
    await testGetRedeemInfo(nikeAuth);
    console.log('─'.repeat(50));
    
    console.log('🎉 测试完成!');
    console.log('💡 提示: 请将 TEST_CONFIG.wxid 替换为你的真实微信ID后再运行测试');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testGetWxCode,
    testNikeLogin,
    testNikeSignIn,
    testGetRedeemInfo,
    runTests
};
