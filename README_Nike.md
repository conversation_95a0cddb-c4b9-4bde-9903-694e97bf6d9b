# Nike小程序自动签到脚本

## 功能介绍

这是一个基于微信小程序授权的Nike自动签到脚本，可以实现：

- ✅ 自动获取微信授权码
- ✅ 自动登录Nike小程序
- ✅ 自动执行每日签到
- ✅ 获取积分奖励
- ✅ 支持多账号批量操作
- ✅ 智能缓存机制，避免重复登录

## 环境要求

- Node.js 环境
- 微信小程序授权服务（wxcode.js依赖的服务）
- Nike小程序账号

## 安装依赖

```bash
npm install request
```

## 配置说明

### 1. 设置微信ID

通过环境变量或命令行参数设置要操作的微信ID：

**环境变量方式：**
```bash
export TXX_WXID="your_wxid_here"
# 或多个账号（换行分隔）
export TXX_WXID="wxid1
wxid2
wxid3"
```

**命令行参数方式：**
```bash
node nike.js --wxid your_wxid_here
```

### 2. 调试模式

启用调试模式查看详细日志：
```bash
node nike.js --debug
```

### 3. 微信授权服务配置

确保 `wxcode.js` 中的服务地址正确配置：
```javascript
let xieyi = process.env.PHONECODE_SERVER || 'http://820121.xyz:8800';
```

## 使用方法

### 基本使用

```bash
# 单个账号签到
node nike.js --wxid your_wxid

# 多个账号签到（使用环境变量）
export TXX_WXID="wxid1
wxid2"
node nike.js

# 调试模式
node nike.js --wxid your_wxid --debug
```

### 定时任务

可以配合cron等工具实现定时自动签到：

```bash
# 每天早上8点自动签到
0 8 * * * cd /path/to/script && node nike.js
```

## 脚本特性

### 智能缓存机制

- 自动缓存微信授权码和Nike登录凭证
- 缓存有效期2小时，避免频繁重新登录
- 自动检测缓存有效性，失效时自动重新获取

### 错误处理

- Token过期自动刷新
- 网络请求失败自动重试
- 详细的错误日志输出

### 通知功能

支持通过 `sendNotify` 模块发送签到结果通知。

## 输出示例

```
🔔 Nike小程序自动签到脚本开始执行
📋 共找到 1 个有效账号

🚀 [1/1] 开始处理账号: your_wxid
📦 使用缓存的数据
✅ 缓存的数据有效
🎯 开始执行Nike每日签到...
🎉 Nike签到成功! 获得 3 积分
✅ [1/1] 账号 your_wxid 处理完成
────────────────────────────────────────────────────────────

🎉 所有账号处理完成！
```

## 注意事项

1. **合规使用**：请确保遵守Nike小程序的使用条款
2. **频率控制**：建议每天签到一次，避免过于频繁的请求
3. **账号安全**：妥善保管微信ID等敏感信息
4. **服务依赖**：脚本依赖第三方微信授权服务，请确保服务可用

## 故障排除

### 常见问题

1. **获取授权码失败**
   - 检查微信ID是否正确
   - 确认授权服务是否可用

2. **Nike登录失败**
   - 检查网络连接
   - 确认Nike小程序服务状态

3. **签到失败**
   - 可能已经签到过了
   - Token可能已过期，脚本会自动重试

### 调试方法

使用 `--debug` 参数查看详细日志：
```bash
node nike.js --wxid your_wxid --debug
```

## 更新日志

### v2.0 (2025-07-30)
- 集成Nike小程序登录和签到功能
- 添加智能缓存机制
- 支持Token自动刷新
- 完善错误处理和日志输出

### v1.0 (2025-07-21)
- 基础微信小程序授权框架
